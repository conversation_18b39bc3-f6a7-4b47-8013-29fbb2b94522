/**
 * واجهة برمجة التطبيقات لقاعدة البيانات الهجين
 * توفر طرق سهلة للتعامل مع البيانات
 */

class DatabaseAPI {
    constructor() {
        this.db = hybridDB;
        this.fallbackToLocalStorage = false;
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async init() {
        const success = await this.db.initialize();
        if (!success) {
            this.fallbackToLocalStorage = true;
            console.log('⚠️ تم التبديل إلى localStorage كنظام احتياطي');
        }
        return success;
    }

    // ==================== إدارة المنتجات ====================

    /**
     * إضافة منتج جديد
     */
    addProduct(product) {
        if (this.fallbackToLocalStorage) {
            return this.addProductLocalStorage(product);
        }

        try {
            const sql = `
                INSERT INTO products (name, category, price, cost, stock, min_stock, barcode, description, image_url)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [
                product.name,
                product.category,
                product.price || 0,
                product.cost || 0,
                product.stock || 0,
                product.minStock || 5,
                product.barcode || null,
                product.description || '',
                product.imageUrl || ''
            ]);

            // تسجيل حركة مخزون إذا كان هناك مخزون أولي
            if (product.stock > 0) {
                this.addInventoryMovement({
                    productName: product.name,
                    movementType: 'in',
                    quantity: product.stock,
                    referenceType: 'initial',
                    notes: 'مخزون أولي'
                });
            }

            return true;
        } catch (error) {
            console.error('خطأ في إضافة المنتج:', error);
            return false;
        }
    }

    /**
     * الحصول على جميع المنتجات
     */
    getProducts() {
        if (this.fallbackToLocalStorage) {
            return this.getProductsLocalStorage();
        }

        try {
            const sql = `
                SELECT * FROM products 
                WHERE is_active = 1 
                ORDER BY name
            `;
            return this.db.queryAll(sql);
        } catch (error) {
            console.error('خطأ في جلب المنتجات:', error);
            return [];
        }
    }

    /**
     * تحديث مخزون المنتج
     */
    updateProductStock(productName, newStock) {
        if (this.fallbackToLocalStorage) {
            return this.updateProductStockLocalStorage(productName, newStock);
        }

        try {
            // الحصول على المخزون الحالي
            const currentProduct = this.db.query(
                'SELECT stock FROM products WHERE name = ?',
                [productName]
            );

            if (!currentProduct) return false;

            const oldStock = currentProduct.stock || 0;
            const difference = newStock - oldStock;

            // تحديث المخزون
            this.db.run(
                'UPDATE products SET stock = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?',
                [newStock, productName]
            );

            // تسجيل حركة المخزون
            if (difference !== 0) {
                this.addInventoryMovement({
                    productName: productName,
                    movementType: difference > 0 ? 'in' : 'out',
                    quantity: Math.abs(difference),
                    referenceType: 'adjustment',
                    notes: 'تعديل مخزون'
                });
            }

            return true;
        } catch (error) {
            console.error('خطأ في تحديث المخزون:', error);
            return false;
        }
    }

    // ==================== إدارة العملاء ====================

    /**
     * إضافة عميل جديد
     */
    addCustomer(customer) {
        if (this.fallbackToLocalStorage) {
            return this.addCustomerLocalStorage(customer);
        }

        try {
            const sql = `
                INSERT INTO customers (name, phone, email, address, type, credit_limit, credit_days, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [
                customer.name,
                customer.phone || null,
                customer.email || null,
                customer.address || '',
                customer.type || 'عادي',
                customer.creditLimit || 1000,
                customer.creditDays || 30,
                customer.notes || ''
            ]);

            return true;
        } catch (error) {
            console.error('خطأ في إضافة العميل:', error);
            return false;
        }
    }

    /**
     * الحصول على جميع العملاء
     */
    getCustomers() {
        if (this.fallbackToLocalStorage) {
            return this.getCustomersLocalStorage();
        }

        try {
            const sql = `
                SELECT * FROM customers 
                WHERE status = 'نشط' 
                ORDER BY name
            `;
            return this.db.queryAll(sql);
        } catch (error) {
            console.error('خطأ في جلب العملاء:', error);
            return [];
        }
    }

    /**
     * تحديث إحصائيات العميل
     */
    updateCustomerStats(customerName, purchaseAmount) {
        if (this.fallbackToLocalStorage) {
            return this.updateCustomerStatsLocalStorage(customerName, purchaseAmount);
        }

        try {
            const sql = `
                UPDATE customers 
                SET total_spent = total_spent + ?,
                    total_purchases = total_purchases + 1,
                    last_purchase_date = CURRENT_TIMESTAMP
                WHERE name = ?
            `;
            
            this.db.run(sql, [purchaseAmount, customerName]);
            return true;
        } catch (error) {
            console.error('خطأ في تحديث إحصائيات العميل:', error);
            return false;
        }
    }

    // ==================== إدارة المبيعات ====================

    /**
     * إضافة عملية بيع جديدة
     */
    addSale(sale) {
        if (this.fallbackToLocalStorage) {
            return this.addSaleLocalStorage(sale);
        }

        try {
            // البحث عن العميل
            let customerId = null;
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                const customer = this.db.query(
                    'SELECT id FROM customers WHERE name = ?',
                    [sale.customerName]
                );
                customerId = customer ? customer.id : null;
            }

            // إضافة المبيعة
            const saleSQL = `
                INSERT INTO sales (invoice_number, customer_id, customer_name, total_amount, 
                                 discount_amount, tax_amount, final_amount, payment_method, 
                                 payment_status, due_date, cashier_name, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(saleSQL, [
                sale.invoiceNumber,
                customerId,
                sale.customerName,
                sale.subtotal || 0,
                sale.discount || 0,
                sale.tax || 0,
                sale.total,
                sale.paymentMethod,
                sale.paymentMethod === 'آجل' ? 'غير مدفوع' : 'مدفوع',
                sale.dueDate || null,
                sale.cashier || 'النظام',
                sale.notes || ''
            ]);

            // الحصول على معرف المبيعة
            const saleId = this.db.query('SELECT last_insert_rowid() as id').id;

            // إضافة عناصر المبيعة
            if (sale.items && sale.items.length > 0) {
                for (const item of sale.items) {
                    this.addSaleItem(saleId, item);
                }
            }

            // تحديث إحصائيات العميل
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                this.updateCustomerStats(sale.customerName, sale.total);
            }

            return true;
        } catch (error) {
            console.error('خطأ في إضافة المبيعة:', error);
            return false;
        }
    }

    /**
     * إضافة عنصر مبيعة
     */
    addSaleItem(saleId, item) {
        try {
            // الحصول على معرف المنتج
            let productId = null;
            const product = this.db.query(
                'SELECT id, cost FROM products WHERE name = ?',
                [item.name]
            );
            
            if (product) {
                productId = product.id;
            }

            const costPrice = product ? product.cost : 0;
            const profit = (item.price * item.quantity) - (costPrice * item.quantity);

            // إضافة العنصر
            const sql = `
                INSERT INTO sale_items (sale_id, product_id, product_name, quantity, 
                                      unit_price, total_price, cost_price, profit)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                saleId,
                productId,
                item.name,
                item.quantity,
                item.price,
                item.price * item.quantity,
                costPrice,
                profit
            ]);

            // تحديث المخزون
            this.updateProductStock(item.name, 
                this.getProductStock(item.name) - item.quantity
            );

            // تسجيل حركة مخزون
            this.addInventoryMovement({
                productName: item.name,
                movementType: 'out',
                quantity: item.quantity,
                referenceType: 'sale',
                referenceId: saleId,
                notes: `بيع - فاتورة رقم ${saleId}`
            });

            return true;
        } catch (error) {
            console.error('خطأ في إضافة عنصر المبيعة:', error);
            return false;
        }
    }

    /**
     * الحصول على المبيعات
     */
    getSales(startDate = null, endDate = null) {
        if (this.fallbackToLocalStorage) {
            return this.getSalesLocalStorage();
        }

        try {
            let sql = 'SELECT * FROM sales ORDER BY sale_date DESC';
            let params = [];

            if (startDate && endDate) {
                sql = `
                    SELECT * FROM sales 
                    WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
                    ORDER BY sale_date DESC
                `;
                params = [startDate, endDate];
            }

            return this.db.queryAll(sql, params);
        } catch (error) {
            console.error('خطأ في جلب المبيعات:', error);
            return [];
        }
    }

    // ==================== إدارة المخزون ====================

    /**
     * إضافة حركة مخزون
     */
    addInventoryMovement(movement) {
        if (this.fallbackToLocalStorage) return true;

        try {
            // الحصول على معرف المنتج
            let productId = null;
            const product = this.db.query(
                'SELECT id FROM products WHERE name = ?',
                [movement.productName]
            );
            
            if (product) {
                productId = product.id;
            }

            const sql = `
                INSERT INTO inventory_movements (product_id, product_name, movement_type, 
                                               quantity, reference_type, reference_id, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                productId,
                movement.productName,
                movement.movementType,
                movement.quantity,
                movement.referenceType || null,
                movement.referenceId || null,
                movement.notes || ''
            ]);

            return true;
        } catch (error) {
            console.error('خطأ في إضافة حركة المخزون:', error);
            return false;
        }
    }

    /**
     * الحصول على مخزون المنتج
     */
    getProductStock(productName) {
        if (this.fallbackToLocalStorage) {
            return this.getProductStockLocalStorage(productName);
        }

        try {
            const product = this.db.query(
                'SELECT stock FROM products WHERE name = ?',
                [productName]
            );
            return product ? product.stock : 0;
        } catch (error) {
            console.error('خطأ في جلب مخزون المنتج:', error);
            return 0;
        }
    }

    // ==================== التقارير المتقدمة ====================

    /**
     * تقرير المبيعات اليومية
     */
    getDailySalesReport(date = null) {
        if (this.fallbackToLocalStorage) {
            return this.getDailySalesReportLocalStorage(date);
        }

        try {
            const targetDate = date || new Date().toISOString().split('T')[0];
            
            const sql = `
                SELECT 
                    COUNT(*) as total_invoices,
                    SUM(final_amount) as total_sales,
                    SUM(discount_amount) as total_discounts,
                    AVG(final_amount) as average_invoice,
                    SUM(CASE WHEN payment_method = 'نقدي' THEN final_amount ELSE 0 END) as cash_sales,
                    SUM(CASE WHEN payment_method = 'بطاقة ائتمان' THEN final_amount ELSE 0 END) as card_sales
                FROM sales 
                WHERE DATE(sale_date) = DATE(?)
            `;

            return this.db.query(sql, [targetDate]);
        } catch (error) {
            console.error('خطأ في تقرير المبيعات اليومية:', error);
            return {};
        }
    }

    /**
     * تقرير أفضل المنتجات مبيعاً
     */
    getTopSellingProducts(limit = 10) {
        if (this.fallbackToLocalStorage) {
            return this.getTopSellingProductsLocalStorage(limit);
        }

        try {
            const sql = `
                SELECT 
                    product_name,
                    SUM(quantity) as total_quantity,
                    SUM(total_price) as total_revenue,
                    SUM(profit) as total_profit,
                    COUNT(*) as times_sold
                FROM sale_items 
                GROUP BY product_name 
                ORDER BY total_quantity DESC 
                LIMIT ?
            `;

            return this.db.queryAll(sql, [limit]);
        } catch (error) {
            console.error('خطأ في تقرير أفضل المنتجات:', error);
            return [];
        }
    }

    // ==================== طرق localStorage الاحتياطية ====================

    addProductLocalStorage(product) {
        const products = JSON.parse(localStorage.getItem('products') || '[]');
        products.push({
            ...product,
            id: Date.now(),
            createdAt: new Date().toISOString()
        });
        localStorage.setItem('products', JSON.stringify(products));
        return true;
    }

    getProductsLocalStorage() {
        return JSON.parse(localStorage.getItem('products') || '[]');
    }

    addCustomerLocalStorage(customer) {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        customers.push({
            ...customer,
            id: Date.now(),
            registrationDate: new Date().toISOString(),
            totalSpent: 0,
            totalPurchases: 0,
            loyaltyPoints: 0
        });
        localStorage.setItem('customers', JSON.stringify(customers));
        return true;
    }

    getCustomersLocalStorage() {
        return JSON.parse(localStorage.getItem('customers') || '[]');
    }

    addSaleLocalStorage(sale) {
        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
        sales.push({
            ...sale,
            id: Date.now(),
            date: new Date().toISOString()
        });
        localStorage.setItem('sales', JSON.stringify(sales));
        return true;
    }

    getSalesLocalStorage() {
        return JSON.parse(localStorage.getItem('sales') || '[]');
    }
}

// إنشاء مثيل واحد من واجهة قاعدة البيانات
const dbAPI = new DatabaseAPI();

// تصدير للاستخدام العام
window.dbAPI = dbAPI;
