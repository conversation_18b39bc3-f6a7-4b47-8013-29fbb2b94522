/**
 * واجهة برمجة التطبيقات لقاعدة البيانات الهجين v2.0
 * توفر طرق سهلة ومحسنة للتعامل مع البيانات
 */

class DatabaseAPI {
    constructor() {
        this.db = hybridDB;
        this.fallbackToLocalStorage = false;
        this.cache = new Map(); // ذاكرة تخزين مؤقت للاستعلامات المتكررة
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async init() {
        const success = await this.db.initialize();
        if (!success) {
            this.fallbackToLocalStorage = true;
            console.log('⚠️ تم التبديل إلى localStorage كنظام احتياطي');
        }
        return success;
    }

    /**
     * مسح ذاكرة التخزين المؤقت
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * الحصول من ذاكرة التخزين المؤقت
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    /**
     * حفظ في ذاكرة التخزين المؤقت
     */
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    // ==================== إدارة المنتجات المحسنة ====================

    /**
     * إضافة منتج جديد
     */
    async addProduct(product) {
        if (this.fallbackToLocalStorage) {
            return this.addProductLocalStorage(product);
        }

        try {
            const sql = `
                INSERT INTO products (
                    name, category, price, cost, stock, min_stock, max_stock,
                    barcode, description, image_url, unit, supplier_id, is_featured
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [
                product.name,
                product.category,
                product.price || 0,
                product.cost || 0,
                product.stock || 0,
                product.minStock || 5,
                product.maxStock || 1000,
                product.barcode || null,
                product.description || '',
                product.imageUrl || '',
                product.unit || 'قطعة',
                product.supplierId || null,
                product.isFeatured || false
            ]);

            // تسجيل حركة مخزون إذا كان هناك مخزون أولي
            if (product.stock > 0) {
                this.addInventoryMovement({
                    productName: product.name,
                    movementType: 'in',
                    quantity: product.stock,
                    referenceType: 'initial',
                    notes: 'مخزون أولي'
                });
            }

            this.clearCache('products');
            return true;
        } catch (error) {
            console.error('خطأ في إضافة المنتج:', error);
            return false;
        }
    }

    /**
     * الحصول على جميع المنتجات مع التخزين المؤقت
     */
    getProducts(activeOnly = true) {
        const cacheKey = `products_${activeOnly}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) return cached;

        if (this.fallbackToLocalStorage) {
            return this.getProductsLocalStorage();
        }

        try {
            let sql = 'SELECT * FROM products';
            if (activeOnly) {
                sql += ' WHERE is_active = 1';
            }
            sql += ' ORDER BY name';

            const products = this.db.queryAll(sql);
            this.setCache(cacheKey, products);
            return products;
        } catch (error) {
            console.error('خطأ في جلب المنتجات:', error);
            return [];
        }
    }

    /**
     * البحث في المنتجات
     */
    searchProducts(searchTerm, category = null) {
        if (this.fallbackToLocalStorage) {
            return this.searchProductsLocalStorage(searchTerm, category);
        }

        try {
            let sql = `
                SELECT * FROM products 
                WHERE is_active = 1 
                AND (name LIKE ? OR barcode LIKE ? OR description LIKE ?)
            `;
            let params = [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`];

            if (category) {
                sql += ' AND category = ?';
                params.push(category);
            }

            sql += ' ORDER BY name LIMIT 50';

            return this.db.queryAll(sql, params);
        } catch (error) {
            console.error('خطأ في البحث في المنتجات:', error);
            return [];
        }
    }

    /**
     * تحديث مخزون المنتج
     */
    updateProductStock(productName, newStock, reason = 'تعديل مخزون') {
        if (this.fallbackToLocalStorage) {
            return this.updateProductStockLocalStorage(productName, newStock);
        }

        try {
            // الحصول على المخزون الحالي
            const currentProduct = this.db.query(
                'SELECT id, stock FROM products WHERE name = ?',
                [productName]
            );

            if (!currentProduct) return false;

            const oldStock = currentProduct.stock || 0;
            const difference = newStock - oldStock;

            // تحديث المخزون
            this.db.run(
                'UPDATE products SET stock = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?',
                [newStock, productName]
            );

            // تسجيل حركة المخزون
            if (difference !== 0) {
                this.addInventoryMovement({
                    productId: currentProduct.id,
                    productName: productName,
                    movementType: difference > 0 ? 'in' : 'out',
                    quantity: Math.abs(difference),
                    oldQuantity: oldStock,
                    newQuantity: newStock,
                    referenceType: 'adjustment',
                    notes: reason
                });
            }

            this.clearCache('products');
            return true;
        } catch (error) {
            console.error('خطأ في تحديث المخزون:', error);
            return false;
        }
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    getLowStockProducts() {
        const cacheKey = 'low_stock_products';
        const cached = this.getFromCache(cacheKey);
        if (cached) return cached;

        if (this.fallbackToLocalStorage) {
            return this.getLowStockProductsLocalStorage();
        }

        try {
            const sql = `
                SELECT * FROM products 
                WHERE is_active = 1 AND stock <= min_stock
                ORDER BY (stock - min_stock) ASC
            `;

            const products = this.db.queryAll(sql);
            this.setCache(cacheKey, products);
            return products;
        } catch (error) {
            console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
            return [];
        }
    }

    // ==================== إدارة العملاء المحسنة ====================

    /**
     * إضافة عميل جديد
     */
    async addCustomer(customer) {
        if (this.fallbackToLocalStorage) {
            return this.addCustomerLocalStorage(customer);
        }

        try {
            const sql = `
                INSERT INTO customers (
                    name, phone, email, address, city, type, credit_limit, 
                    credit_days, birth_date, gender, notes, discount_percentage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [
                customer.name,
                customer.phone || null,
                customer.email || null,
                customer.address || '',
                customer.city || '',
                customer.type || 'عادي',
                customer.creditLimit || 1000,
                customer.creditDays || 30,
                customer.birthDate || null,
                customer.gender || null,
                customer.notes || '',
                customer.discountPercentage || 0
            ]);

            this.clearCache('customers');
            return true;
        } catch (error) {
            console.error('خطأ في إضافة العميل:', error);
            return false;
        }
    }

    /**
     * الحصول على جميع العملاء مع التخزين المؤقت
     */
    getCustomers(activeOnly = true) {
        const cacheKey = `customers_${activeOnly}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) return cached;

        if (this.fallbackToLocalStorage) {
            return this.getCustomersLocalStorage();
        }

        try {
            let sql = 'SELECT * FROM customers';
            if (activeOnly) {
                sql += ' WHERE status = "نشط"';
            }
            sql += ' ORDER BY name';

            const customers = this.db.queryAll(sql);
            this.setCache(cacheKey, customers);
            return customers;
        } catch (error) {
            console.error('خطأ في جلب العملاء:', error);
            return [];
        }
    }

    /**
     * البحث في العملاء
     */
    searchCustomers(searchTerm) {
        if (this.fallbackToLocalStorage) {
            return this.searchCustomersLocalStorage(searchTerm);
        }

        try {
            const sql = `
                SELECT * FROM customers 
                WHERE status = 'نشط' 
                AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)
                ORDER BY name LIMIT 20
            `;

            return this.db.queryAll(sql, [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`]);
        } catch (error) {
            console.error('خطأ في البحث في العملاء:', error);
            return [];
        }
    }

    /**
     * تحديث إحصائيات العميل
     */
    updateCustomerStats(customerName, purchaseAmount, loyaltyPoints = 0) {
        if (this.fallbackToLocalStorage) {
            return this.updateCustomerStatsLocalStorage(customerName, purchaseAmount);
        }

        try {
            const sql = `
                UPDATE customers 
                SET total_spent = total_spent + ?,
                    total_purchases = total_purchases + 1,
                    loyalty_points = loyalty_points + ?,
                    total_earned_points = total_earned_points + ?,
                    last_purchase_date = CURRENT_TIMESTAMP
                WHERE name = ?
            `;
            
            this.db.run(sql, [purchaseAmount, loyaltyPoints, loyaltyPoints, customerName]);
            this.clearCache('customers');
            return true;
        } catch (error) {
            console.error('خطأ في تحديث إحصائيات العميل:', error);
            return false;
        }
    }

    // ==================== إدارة المبيعات المحسنة ====================

    /**
     * إضافة عملية بيع جديدة
     */
    async addSale(sale) {
        if (this.fallbackToLocalStorage) {
            return this.addSaleLocalStorage(sale);
        }

        try {
            // البحث عن العميل
            let customerId = null;
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                const customer = this.db.query(
                    'SELECT id FROM customers WHERE name = ?',
                    [sale.customerName]
                );
                customerId = customer ? customer.id : null;
            }

            // إضافة المبيعة
            const saleSQL = `
                INSERT INTO sales (
                    invoice_number, customer_id, customer_name, total_amount, 
                    discount_amount, discount_percentage, tax_amount, tax_percentage,
                    final_amount, payment_method, payment_status, due_date, 
                    cashier_name, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(saleSQL, [
                sale.invoiceNumber,
                customerId,
                sale.customerName,
                sale.subtotal || 0,
                sale.discountAmount || 0,
                sale.discountPercentage || 0,
                sale.taxAmount || 0,
                sale.taxPercentage || 0,
                sale.total,
                sale.paymentMethod,
                sale.paymentMethod === 'آجل' ? 'غير مدفوع' : 'مدفوع',
                sale.dueDate || null,
                sale.cashier || 'النظام',
                sale.notes || ''
            ]);

            // الحصول على معرف المبيعة
            const saleId = this.db.query('SELECT last_insert_rowid() as id').id;

            // إضافة عناصر المبيعة
            if (sale.items && sale.items.length > 0) {
                for (const item of sale.items) {
                    await this.addSaleItem(saleId, item);
                }
            }

            // تحديث إحصائيات العميل
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                const loyaltyPoints = Math.floor(sale.total * 0.01); // نقطة لكل شيكل
                this.updateCustomerStats(sale.customerName, sale.total, loyaltyPoints);
                
                // إضافة معاملة نقاط الولاء
                if (loyaltyPoints > 0 && customerId) {
                    this.addLoyaltyTransaction(customerId, 'earned', loyaltyPoints, 'sale', saleId);
                }
            }

            this.clearCache();
            return true;
        } catch (error) {
            console.error('خطأ في إضافة المبيعة:', error);
            return false;
        }
    }

    /**
     * إضافة عنصر مبيعة
     */
    async addSaleItem(saleId, item) {
        try {
            // الحصول على معرف المنتج وتكلفته
            let productId = null;
            let costPrice = 0;
            
            const product = this.db.query(
                'SELECT id, cost FROM products WHERE name = ?',
                [item.name]
            );
            
            if (product) {
                productId = product.id;
                costPrice = product.cost || 0;
            }

            const totalPrice = item.price * item.quantity;
            const profit = totalPrice - (costPrice * item.quantity);

            // إضافة العنصر
            const sql = `
                INSERT INTO sale_items (
                    sale_id, product_id, product_name, quantity, 
                    unit_price, total_price, cost_price, profit,
                    discount_amount, tax_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                saleId,
                productId,
                item.name,
                item.quantity,
                item.price,
                totalPrice,
                costPrice,
                profit,
                item.discountAmount || 0,
                item.taxAmount || 0
            ]);

            // تحديث المخزون
            if (productId) {
                const currentStock = this.getProductStock(item.name);
                this.updateProductStock(item.name, currentStock - item.quantity, `بيع - فاتورة رقم ${saleId}`);
            }

            // تسجيل حركة مخزون
            this.addInventoryMovement({
                productId: productId,
                productName: item.name,
                movementType: 'out',
                quantity: item.quantity,
                referenceType: 'sale',
                referenceId: saleId,
                costPerUnit: costPrice,
                notes: `بيع - فاتورة رقم ${saleId}`
            });

            return true;
        } catch (error) {
            console.error('خطأ في إضافة عنصر المبيعة:', error);
            return false;
        }
    }

    /**
     * الحصول على المبيعات
     */
    getSales(startDate = null, endDate = null, limit = 100) {
        if (this.fallbackToLocalStorage) {
            return this.getSalesLocalStorage();
        }

        try {
            let sql = 'SELECT * FROM sales';
            let params = [];

            if (startDate && endDate) {
                sql += ' WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)';
                params = [startDate, endDate];
            }

            sql += ' ORDER BY sale_date DESC LIMIT ?';
            params.push(limit);

            return this.db.queryAll(sql, params);
        } catch (error) {
            console.error('خطأ في جلب المبيعات:', error);
            return [];
        }
    }

    // ==================== إدارة المخزون المحسنة ====================

    /**
     * إضافة حركة مخزون
     */
    addInventoryMovement(movement) {
        if (this.fallbackToLocalStorage) return true;

        try {
            const sql = `
                INSERT INTO inventory_movements (
                    product_id, product_name, movement_type, quantity, 
                    old_quantity, new_quantity, reference_type, reference_id, 
                    cost_per_unit, notes, user_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                movement.productId || null,
                movement.productName,
                movement.movementType,
                movement.quantity,
                movement.oldQuantity || 0,
                movement.newQuantity || 0,
                movement.referenceType || null,
                movement.referenceId || null,
                movement.costPerUnit || 0,
                movement.notes || '',
                movement.userName || 'النظام'
            ]);

            return true;
        } catch (error) {
            console.error('خطأ في إضافة حركة المخزون:', error);
            return false;
        }
    }

    /**
     * الحصول على مخزون المنتج
     */
    getProductStock(productName) {
        if (this.fallbackToLocalStorage) {
            return this.getProductStockLocalStorage(productName);
        }

        try {
            const product = this.db.query(
                'SELECT stock FROM products WHERE name = ?',
                [productName]
            );
            return product ? product.stock : 0;
        } catch (error) {
            console.error('خطأ في جلب مخزون المنتج:', error);
            return 0;
        }
    }

    // ==================== إدارة نقاط الولاء ====================

    /**
     * إضافة معاملة نقاط ولاء
     */
    addLoyaltyTransaction(customerId, type, points, referenceType = null, referenceId = null, description = '') {
        if (this.fallbackToLocalStorage) return true;

        try {
            const sql = `
                INSERT INTO loyalty_transactions (
                    customer_id, transaction_type, points, reference_type, 
                    reference_id, description
                ) VALUES (?, ?, ?, ?, ?, ?)
            `;

            this.db.run(sql, [
                customerId,
                type,
                points,
                referenceType,
                referenceId,
                description
            ]);

            return true;
        } catch (error) {
            console.error('خطأ في إضافة معاملة نقاط الولاء:', error);
            return false;
        }
    }

    // ==================== طرق النظام الاحتياطي ====================

    addProductLocalStorage(product) {
        const products = JSON.parse(localStorage.getItem('products') || '[]');
        products.push({
            ...product,
            id: Date.now(),
            createdAt: new Date().toISOString()
        });
        localStorage.setItem('products', JSON.stringify(products));
        return true;
    }

    getProductsLocalStorage() {
        return JSON.parse(localStorage.getItem('products') || '[]');
    }

    addCustomerLocalStorage(customer) {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        customers.push({
            ...customer,
            id: Date.now(),
            registrationDate: new Date().toISOString(),
            totalSpent: 0,
            totalPurchases: 0,
            loyaltyPoints: 0
        });
        localStorage.setItem('customers', JSON.stringify(customers));
        return true;
    }

    getCustomersLocalStorage() {
        return JSON.parse(localStorage.getItem('customers') || '[]');
    }

    addSaleLocalStorage(sale) {
        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
        sales.push({
            ...sale,
            id: Date.now(),
            date: new Date().toISOString()
        });
        localStorage.setItem('sales', JSON.stringify(sales));
        return true;
    }

    getSalesLocalStorage() {
        return JSON.parse(localStorage.getItem('sales') || '[]');
    }

    searchProductsLocalStorage(searchTerm, category) {
        const products = this.getProductsLocalStorage();
        return products.filter(product => 
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
            (!category || product.category === category)
        );
    }

    searchCustomersLocalStorage(searchTerm) {
        const customers = this.getCustomersLocalStorage();
        return customers.filter(customer => 
            customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (customer.phone && customer.phone.includes(searchTerm))
        );
    }

    getLowStockProductsLocalStorage() {
        const products = this.getProductsLocalStorage();
        return products.filter(product => 
            (product.stock || 0) <= (product.minStock || 5)
        );
    }

    updateProductStockLocalStorage(productName, newStock) {
        const products = this.getProductsLocalStorage();
        const productIndex = products.findIndex(p => p.name === productName);
        if (productIndex !== -1) {
            products[productIndex].stock = newStock;
            localStorage.setItem('products', JSON.stringify(products));
            return true;
        }
        return false;
    }

    updateCustomerStatsLocalStorage(customerName, purchaseAmount) {
        const customers = this.getCustomersLocalStorage();
        const customerIndex = customers.findIndex(c => c.name === customerName);
        if (customerIndex !== -1) {
            customers[customerIndex].totalSpent = (customers[customerIndex].totalSpent || 0) + purchaseAmount;
            customers[customerIndex].totalPurchases = (customers[customerIndex].totalPurchases || 0) + 1;
            customers[customerIndex].lastPurchase = new Date().toISOString();
            localStorage.setItem('customers', JSON.stringify(customers));
            return true;
        }
        return false;
    }

    getProductStockLocalStorage(productName) {
        const products = this.getProductsLocalStorage();
        const product = products.find(p => p.name === productName);
        return product ? (product.stock || 0) : 0;
    }
}

// إنشاء مثيل واحد من واجهة قاعدة البيانات
const dbAPI = new DatabaseAPI();

// تصدير للاستخدام العام
window.dbAPI = dbAPI;
