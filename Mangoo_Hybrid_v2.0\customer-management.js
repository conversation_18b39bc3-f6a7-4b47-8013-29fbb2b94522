/**
 * نظام إدارة العملاء للنسخة الهجين v2.0
 * محسن للأداء والسرعة
 */

class CustomerManagement {
    constructor() {
        this.isInitialized = false;
        this.currentView = 'customers';
    }

    /**
     * تهيئة نظام إدارة العملاء
     */
    async initialize() {
        if (this.isInitialized) return;
        
        console.log('👥 تهيئة نظام إدارة العملاء الهجين...');
        
        // إنشاء واجهة إدارة العملاء
        this.createCustomerInterface();
        
        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام إدارة العملاء');
    }

    /**
     * إنشاء واجهة إدارة العملاء
     */
    createCustomerInterface() {
        const customerHTML = `
            <div class="customer-container" style="display: none;" id="customerContainer">
                <div class="customer-header">
                    <h2>👥 إدارة العملاء الهجين</h2>
                    <button class="btn btn-secondary" onclick="closeCustomers()">إغلاق</button>
                </div>
                
                <div class="customer-nav">
                    <button class="btn btn-primary" onclick="switchCustomerView('customers')">قائمة العملاء</button>
                    <button class="btn btn-outline-primary" onclick="switchCustomerView('reports')">التقارير المتقدمة</button>
                    <button class="btn btn-success" onclick="showAddCustomerModal()">إضافة عميل</button>
                </div>
                
                <div class="customer-content">
                    <!-- عرض العملاء -->
                    <div id="customersView" class="customer-view">
                        <div class="search-bar">
                            <input type="text" id="customerSearchInput" placeholder="ابحث في العملاء..." 
                                   onkeyup="searchCustomers(this.value)" class="form-control">
                        </div>
                        <div class="customers-table-container">
                            <table class="table" id="customersTable">
                                <thead>
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>الهاتف</th>
                                        <th>النوع</th>
                                        <th>إجمالي الإنفاق</th>
                                        <th>عدد المشتريات</th>
                                        <th>نقاط الولاء</th>
                                        <th>آخر شراء</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="customersTableBody">
                                    <!-- العملاء سيظهرون هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- عرض التقارير -->
                    <div id="reportsView" class="customer-view" style="display: none;">
                        <div class="reports-container" id="reportsContainer">
                            <!-- التقارير ستظهر هنا -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نافذة إضافة عميل -->
            <div class="modal" id="addCustomerModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إضافة عميل جديد</h3>
                        <button onclick="closeAddCustomerModal()" class="btn btn-secondary">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="addCustomerForm">
                            <div class="form-group">
                                <label>اسم العميل:</label>
                                <input type="text" id="customerName" class="form-control" required>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>رقم الهاتف:</label>
                                    <input type="tel" id="customerPhone" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>البريد الإلكتروني:</label>
                                    <input type="email" id="customerEmail" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>العنوان:</label>
                                <input type="text" id="customerAddress" class="form-control">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المدينة:</label>
                                    <input type="text" id="customerCity" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>نوع العميل:</label>
                                    <select id="customerType" class="form-control">
                                        <option value="عادي">عادي</option>
                                        <option value="مميز">مميز</option>
                                        <option value="VIP">VIP</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>حد الائتمان:</label>
                                    <input type="number" id="customerCreditLimit" class="form-control" value="1000" min="0">
                                </div>
                                <div class="form-group">
                                    <label>أيام الائتمان:</label>
                                    <input type="number" id="customerCreditDays" class="form-control" value="30" min="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>تاريخ الميلاد:</label>
                                    <input type="date" id="customerBirthDate" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>الجنس:</label>
                                    <select id="customerGender" class="form-control">
                                        <option value="">غير محدد</option>
                                        <option value="ذكر">ذكر</option>
                                        <option value="أنثى">أنثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات:</label>
                                <textarea id="customerNotes" class="form-control" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button onclick="saveCustomer()" class="btn btn-success">حفظ</button>
                        <button onclick="closeAddCustomerModal()" class="btn btn-secondary">إلغاء</button>
                    </div>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const customerStyles = `
            <style>
                .customer-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: white;
                    z-index: 10000;
                    overflow-y: auto;
                }
                
                .customer-header {
                    background: #2c3e50;
                    color: white;
                    padding: 1rem 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .customer-nav {
                    background: #34495e;
                    padding: 1rem 2rem;
                    display: flex;
                    gap: 1rem;
                }
                
                .customer-content {
                    padding: 2rem;
                }
                
                .customer-view {
                    min-height: 500px;
                }
                
                .customers-table-container {
                    max-height: 600px;
                    overflow-y: auto;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                
                .customer-type-badge {
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.8rem;
                    font-weight: bold;
                }
                
                .type-normal {
                    background: #e9ecef;
                    color: #495057;
                }
                
                .type-premium {
                    background: #cce5ff;
                    color: #0066cc;
                }
                
                .type-vip {
                    background: #fff3cd;
                    color: #856404;
                }
                
                .status-active {
                    color: #28a745;
                    font-weight: bold;
                }
                
                .status-inactive {
                    color: #dc3545;
                    font-weight: bold;
                }
                
                .reports-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                }
                
                .report-card {
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 1.5rem;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                
                .report-card h4 {
                    margin-bottom: 1rem;
                    color: #2c3e50;
                }
                
                .stat-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.5rem 0;
                    border-bottom: 1px solid #eee;
                }
                
                .stat-item:last-child {
                    border-bottom: none;
                }
                
                .stat-value {
                    font-weight: bold;
                    color: #007bff;
                }
            </style>
        `;

        // إضافة الواجهة والأنماط إلى الصفحة
        document.head.insertAdjacentHTML('beforeend', customerStyles);
        document.body.insertAdjacentHTML('beforeend', customerHTML);
    }

    /**
     * فتح إدارة العملاء
     */
    async openCustomers() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        document.getElementById('customerContainer').style.display = 'block';
        await this.loadCustomers();
    }

    /**
     * إغلاق إدارة العملاء
     */
    closeCustomers() {
        document.getElementById('customerContainer').style.display = 'none';
    }

    /**
     * تبديل العرض
     */
    switchView(view) {
        // إخفاء جميع العروض
        document.querySelectorAll('.customer-view').forEach(v => v.style.display = 'none');
        
        // إظهار العرض المطلوب
        document.getElementById(view + 'View').style.display = 'block';
        
        // تحديث أزرار التنقل
        document.querySelectorAll('.customer-nav .btn').forEach(btn => {
            btn.className = btn.className.replace('btn-primary', 'btn-outline-primary');
        });
        
        event.target.className = event.target.className.replace('btn-outline-primary', 'btn-primary');
        
        this.currentView = view;
        
        // تحميل البيانات حسب العرض
        switch(view) {
            case 'customers':
                this.loadCustomers();
                break;
            case 'reports':
                this.loadReports();
                break;
        }
    }

    /**
     * تحميل العملاء
     */
    async loadCustomers() {
        try {
            const customers = dbAPI.getCustomers(true);
            this.displayCustomers(customers);
        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
        }
    }

    /**
     * عرض العملاء
     */
    displayCustomers(customers) {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        tbody.innerHTML = customers.map(customer => {
            const typeClass = this.getCustomerTypeClass(customer.type);
            const statusClass = customer.status === 'نشط' ? 'status-active' : 'status-inactive';
            const lastPurchase = customer.last_purchase_date ? 
                new Date(customer.last_purchase_date).toLocaleDateString('ar-SA') : 'لا يوجد';

            return `
                <tr>
                    <td><strong>${customer.name}</strong></td>
                    <td>${customer.phone || '-'}</td>
                    <td><span class="customer-type-badge ${typeClass}">${customer.type || 'عادي'}</span></td>
                    <td>${(customer.total_spent || 0).toFixed(2)} ₪</td>
                    <td>${customer.total_purchases || 0}</td>
                    <td>${customer.loyalty_points || 0}</td>
                    <td>${lastPurchase}</td>
                    <td><span class="${statusClass}">${customer.status || 'نشط'}</span></td>
                    <td>
                        <button onclick="editCustomer(${customer.id})" class="btn btn-sm btn-primary">تعديل</button>
                        <button onclick="viewCustomerDetails(${customer.id})" class="btn btn-sm btn-info">التفاصيل</button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * الحصول على فئة نوع العميل
     */
    getCustomerTypeClass(type) {
        switch(type) {
            case 'VIP': return 'type-vip';
            case 'مميز': return 'type-premium';
            default: return 'type-normal';
        }
    }

    /**
     * تحميل التقارير
     */
    async loadReports() {
        try {
            if (window.advancedReports && !dbAPI.fallbackToLocalStorage) {
                const report = await advancedReports.generateAdvancedCustomersReport();
                this.displayAdvancedReports(report);
            } else {
                this.displayBasicReports();
            }
        } catch (error) {
            console.error('خطأ في تحميل التقارير:', error);
            this.displayBasicReports();
        }
    }

    /**
     * عرض التقارير المتقدمة
     */
    displayAdvancedReports(report) {
        const container = document.getElementById('reportsContainer');
        if (!container) return;

        const summary = report.summary;
        const segmentation = report.segmentation || [];
        const topSpenders = report.topSpenders || [];

        container.innerHTML = `
            <div class="report-card">
                <h4>📊 ملخص العملاء</h4>
                <div class="stat-item">
                    <span>إجمالي العملاء:</span>
                    <span class="stat-value">${summary.total_customers || 0}</span>
                </div>
                <div class="stat-item">
                    <span>العملاء النشطين:</span>
                    <span class="stat-value">${summary.active_customers || 0}</span>
                </div>
                <div class="stat-item">
                    <span>عملاء VIP:</span>
                    <span class="stat-value">${summary.vip_customers || 0}</span>
                </div>
                <div class="stat-item">
                    <span>إجمالي الإنفاق:</span>
                    <span class="stat-value">${(summary.total_customer_spending || 0).toFixed(2)} ₪</span>
                </div>
                <div class="stat-item">
                    <span>متوسط الإنفاق:</span>
                    <span class="stat-value">${(summary.avg_customer_spending || 0).toFixed(2)} ₪</span>
                </div>
            </div>

            <div class="report-card">
                <h4>🎯 تجزئة العملاء</h4>
                ${segmentation.map(seg => `
                    <div class="stat-item">
                        <span>${seg.segment}:</span>
                        <span class="stat-value">${seg.customer_count} عميل</span>
                    </div>
                `).join('')}
            </div>

            <div class="report-card">
                <h4>🏆 أفضل 5 عملاء</h4>
                ${topSpenders.slice(0, 5).map((customer, index) => `
                    <div class="stat-item">
                        <span>${index + 1}. ${customer.name}</span>
                        <span class="stat-value">${(customer.total_spent || 0).toFixed(2)} ₪</span>
                    </div>
                `).join('')}
            </div>

            <div class="report-card">
                <h4>💎 نقاط الولاء</h4>
                <div class="stat-item">
                    <span>إجمالي النقاط:</span>
                    <span class="stat-value">${(summary.total_loyalty_points || 0).toLocaleString()}</span>
                </div>
                <div class="stat-item">
                    <span>نشط الشهر الماضي:</span>
                    <span class="stat-value">${summary.active_last_month || 0}</span>
                </div>
                <div class="stat-item">
                    <span>عملاء جدد الشهر:</span>
                    <span class="stat-value">${summary.new_customers_month || 0}</span>
                </div>
            </div>
        `;
    }

    /**
     * عرض التقارير الأساسية
     */
    displayBasicReports() {
        const container = document.getElementById('reportsContainer');
        if (!container) return;

        const customers = dbAPI.getCustomers(true);
        const totalSpent = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);
        const avgSpent = totalSpent / customers.length || 0;
        const vipCustomers = customers.filter(c => c.type === 'VIP').length;

        container.innerHTML = `
            <div class="report-card">
                <h4>📊 ملخص العملاء (أساسي)</h4>
                <div class="stat-item">
                    <span>إجمالي العملاء:</span>
                    <span class="stat-value">${customers.length}</span>
                </div>
                <div class="stat-item">
                    <span>عملاء VIP:</span>
                    <span class="stat-value">${vipCustomers}</span>
                </div>
                <div class="stat-item">
                    <span>إجمالي الإنفاق:</span>
                    <span class="stat-value">${totalSpent.toFixed(2)} ₪</span>
                </div>
                <div class="stat-item">
                    <span>متوسط الإنفاق:</span>
                    <span class="stat-value">${avgSpent.toFixed(2)} ₪</span>
                </div>
            </div>
            
            <div class="alert alert-info">
                <strong>ملاحظة:</strong> للحصول على تقارير متقدمة، تأكد من تشغيل النظام الهجين
            </div>
        `;
    }

    /**
     * حفظ عميل جديد
     */
    async saveCustomer() {
        try {
            const customer = {
                name: document.getElementById('customerName').value,
                phone: document.getElementById('customerPhone').value,
                email: document.getElementById('customerEmail').value,
                address: document.getElementById('customerAddress').value,
                city: document.getElementById('customerCity').value,
                type: document.getElementById('customerType').value,
                creditLimit: parseFloat(document.getElementById('customerCreditLimit').value) || 1000,
                creditDays: parseInt(document.getElementById('customerCreditDays').value) || 30,
                birthDate: document.getElementById('customerBirthDate').value || null,
                gender: document.getElementById('customerGender').value || null,
                notes: document.getElementById('customerNotes').value
            };

            if (!customer.name) {
                alert('يرجى إدخال اسم العميل');
                return;
            }

            const success = await dbAPI.addCustomer(customer);
            
            if (success) {
                alert('تم إضافة العميل بنجاح');
                this.closeAddCustomerModal();
                this.loadCustomers();
            } else {
                alert('حدث خطأ في إضافة العميل');
            }
        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            alert('حدث خطأ في حفظ العميل');
        }
    }

    /**
     * إظهار نافذة إضافة عميل
     */
    showAddCustomerModal() {
        document.getElementById('addCustomerModal').style.display = 'flex';
    }

    /**
     * إغلاق نافذة إضافة عميل
     */
    closeAddCustomerModal() {
        document.getElementById('addCustomerModal').style.display = 'none';
        document.getElementById('addCustomerForm').reset();
    }
}

// إنشاء مثيل من نظام إدارة العملاء
const customerManagement = new CustomerManagement();

// الوظائف العامة
window.manageCustomers = async function() {
    await customerManagement.openCustomers();
};

window.closeCustomers = function() {
    customerManagement.closeCustomers();
};

window.switchCustomerView = function(view) {
    customerManagement.switchView(view);
};

window.showAddCustomerModal = function() {
    customerManagement.showAddCustomerModal();
};

window.closeAddCustomerModal = function() {
    customerManagement.closeAddCustomerModal();
};

window.saveCustomer = async function() {
    await customerManagement.saveCustomer();
};

window.searchCustomers = function(searchTerm) {
    if (searchTerm.length < 2) {
        customerManagement.loadCustomers();
        return;
    }
    
    const customers = dbAPI.searchCustomers(searchTerm);
    customerManagement.displayCustomers(customers);
};

window.editCustomer = function(customerId) {
    alert('ميزة تعديل العميل ستكون متاحة قريباً');
};

window.viewCustomerDetails = function(customerId) {
    alert('ميزة عرض تفاصيل العميل ستكون متاحة قريباً');
};

// تصدير للاستخدام العام
window.customerManagement = customerManagement;
