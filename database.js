/**
 * نظام قاعدة البيانات الهجين - SQLite + JavaScript
 * يجمع بين سهولة النظام الحالي وقوة قواعد البيانات
 * محمول 100% - يعمل في المتصفح بدون خادم
 */

class HybridDatabase {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.backupInterval = null;
        
        // إعدادات قاعدة البيانات
        this.dbConfig = {
            name: 'mangoo_hybrid.db',
            version: '1.0',
            autoBackup: true,
            backupInterval: 30 * 60 * 1000, // 30 دقيقة
            encryption: true
        };
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async initialize() {
        try {
            console.log('🔄 تهيئة قاعدة البيانات الهجين...');
            
            // تحميل SQL.js
            await this.loadSQLJS();
            
            // إنشاء أو تحميل قاعدة البيانات
            await this.createOrLoadDatabase();
            
            // إنشاء الجداول
            await this.createTables();
            
            // ترحيل البيانات من localStorage إذا لزم الأمر
            await this.migrateFromLocalStorage();
            
            // بدء النسخ الاحتياطي التلقائي
            this.startAutoBackup();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            
            // العودة إلى localStorage في حالة الفشل
            console.log('🔄 العودة إلى نظام localStorage...');
            return false;
        }
    }

    /**
     * تحميل مكتبة SQL.js
     */
    async loadSQLJS() {
        return new Promise((resolve, reject) => {
            if (window.SQL) {
                resolve();
                return;
            }

            // تحميل SQL.js من CDN
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js';
            script.onload = async () => {
                try {
                    // تهيئة SQL.js
                    const SQL = await initSqlJs({
                        locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                    });
                    window.SQL = SQL;
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * إنشاء أو تحميل قاعدة البيانات
     */
    async createOrLoadDatabase() {
        try {
            // محاولة تحميل قاعدة بيانات موجودة
            const savedDb = localStorage.getItem('mangoo_hybrid_db');
            
            if (savedDb) {
                // تحميل قاعدة البيانات الموجودة
                const dbData = new Uint8Array(JSON.parse(savedDb));
                this.db = new window.SQL.Database(dbData);
                console.log('📂 تم تحميل قاعدة البيانات الموجودة');
            } else {
                // إنشاء قاعدة بيانات جديدة
                this.db = new window.SQL.Database();
                console.log('🆕 تم إنشاء قاعدة بيانات جديدة');
            }
        } catch (error) {
            console.error('خطأ في إنشاء/تحميل قاعدة البيانات:', error);
            this.db = new window.SQL.Database();
        }
    }

    /**
     * إنشاء الجداول الأساسية
     */
    async createTables() {
        const tables = [
            // جدول المنتجات
            `CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                cost REAL DEFAULT 0,
                stock INTEGER DEFAULT 0,
                min_stock INTEGER DEFAULT 5,
                barcode TEXT UNIQUE,
                description TEXT,
                image_url TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول العملاء
            `CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT UNIQUE,
                email TEXT,
                address TEXT,
                type TEXT DEFAULT 'عادي',
                credit_limit REAL DEFAULT 1000,
                credit_days INTEGER DEFAULT 30,
                total_spent REAL DEFAULT 0,
                total_purchases INTEGER DEFAULT 0,
                loyalty_points INTEGER DEFAULT 0,
                total_earned_points INTEGER DEFAULT 0,
                total_redeemed_points INTEGER DEFAULT 0,
                status TEXT DEFAULT 'نشط',
                last_purchase_date DATETIME,
                registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )`,

            // جدول المبيعات
            `CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                customer_name TEXT,
                total_amount REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                payment_status TEXT DEFAULT 'مدفوع',
                due_date DATETIME,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                cashier_name TEXT,
                notes TEXT,
                is_refunded BOOLEAN DEFAULT 0,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )`,

            // جدول تفاصيل المبيعات
            `CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER,
                product_name TEXT NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                cost_price REAL DEFAULT 0,
                profit REAL DEFAULT 0,
                FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول الموردين
            `CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                total_purchases REAL DEFAULT 0,
                last_purchase_date DATETIME,
                status TEXT DEFAULT 'نشط',
                payment_terms TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول المشتريات
            `CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE,
                supplier_id INTEGER,
                supplier_name TEXT,
                total_amount REAL NOT NULL,
                payment_method TEXT,
                payment_status TEXT DEFAULT 'مدفوع',
                purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                due_date DATETIME,
                notes TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )`,

            // جدول تفاصيل المشتريات
            `CREATE TABLE IF NOT EXISTS purchase_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_id INTEGER NOT NULL,
                product_id INTEGER,
                product_name TEXT NOT NULL,
                quantity REAL NOT NULL,
                unit_cost REAL NOT NULL,
                total_cost REAL NOT NULL,
                FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول حركات المخزون
            `CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                product_name TEXT NOT NULL,
                movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                quantity REAL NOT NULL,
                reference_type TEXT, -- 'sale', 'purchase', 'adjustment', 'production'
                reference_id INTEGER,
                notes TEXT,
                movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول نقاط الولاء
            `CREATE TABLE IF NOT EXISTS loyalty_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL, -- 'earned', 'redeemed'
                points INTEGER NOT NULL,
                reference_type TEXT, -- 'sale', 'manual', 'bonus'
                reference_id INTEGER,
                description TEXT,
                transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )`,

            // جدول الإعدادات
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        // إنشاء الجداول
        for (const tableSQL of tables) {
            try {
                this.db.run(tableSQL);
            } catch (error) {
                console.error('خطأ في إنشاء جدول:', error);
            }
        }

        // إنشاء الفهارس لتحسين الأداء
        this.createIndexes();
        
        console.log('✅ تم إنشاء جميع الجداول والفهارس');
    }

    /**
     * إنشاء الفهارس لتحسين الأداء
     */
    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
            'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)',
            'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)',
            'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
            'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)',
            'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)',
            'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)',
            'CREATE INDEX IF NOT EXISTS idx_sales_invoice ON sales(invoice_number)',
            'CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)',
            'CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)',
            'CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)',
            'CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(movement_date)',
            'CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_transactions(customer_id)',
            'CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)'
        ];

        indexes.forEach(indexSQL => {
            try {
                this.db.run(indexSQL);
            } catch (error) {
                console.error('خطأ في إنشاء فهرس:', error);
            }
        });
    }

    /**
     * حفظ قاعدة البيانات في localStorage
     */
    saveDatabase() {
        try {
            if (!this.db) return false;
            
            const data = this.db.export();
            const dataArray = Array.from(data);
            localStorage.setItem('mangoo_hybrid_db', JSON.stringify(dataArray));
            
            // حفظ معلومات النسخة الاحتياطية
            localStorage.setItem('mangoo_db_backup_info', JSON.stringify({
                lastBackup: new Date().toISOString(),
                size: dataArray.length,
                version: this.dbConfig.version
            }));
            
            return true;
        } catch (error) {
            console.error('خطأ في حفظ قاعدة البيانات:', error);
            return false;
        }
    }

    /**
     * بدء النسخ الاحتياطي التلقائي
     */
    startAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
        }

        if (this.dbConfig.autoBackup) {
            this.backupInterval = setInterval(() => {
                this.saveDatabase();
                console.log('💾 تم إنشاء نسخة احتياطية تلقائية');
            }, this.dbConfig.backupInterval);
        }
    }

    /**
     * إيقاف النسخ الاحتياطي التلقائي
     */
    stopAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
    }

    /**
     * تنفيذ استعلام SQL
     */
    query(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            const result = stmt.getAsObject(params);
            stmt.free();
            
            return result;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }

    /**
     * تنفيذ استعلام وإرجاع جميع النتائج
     */
    queryAll(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            const results = [];
            
            while (stmt.step()) {
                results.push(stmt.getAsObject());
            }
            
            stmt.free();
            return results;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }

    /**
     * تنفيذ استعلام تعديل (INSERT, UPDATE, DELETE)
     */
    run(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            stmt.run(params);
            stmt.free();
            
            // حفظ التغييرات
            this.saveDatabase();
            
            return true;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const hybridDB = new HybridDatabase();

// تصدير للاستخدام العام
window.hybridDB = hybridDB;
