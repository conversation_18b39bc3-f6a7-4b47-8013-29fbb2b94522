/**
 * نظام قاعدة البيانات الهجين - SQLite + JavaScript
 * النسخة المحسنة والمنفصلة
 * محمول 100% - يعمل في المتصفح بدون خادم
 */

class HybridDatabase {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.backupInterval = null;
        
        // إعدادات قاعدة البيانات
        this.dbConfig = {
            name: 'mangoo_hybrid_v2.db',
            version: '2.0',
            autoBackup: true,
            backupInterval: 30 * 60 * 1000, // 30 دقيقة
            encryption: true
        };
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async initialize() {
        try {
            console.log('🔄 تهيئة قاعدة البيانات الهجين v2.0...');
            
            // تحميل SQL.js
            await this.loadSQLJS();
            
            // إنشاء أو تحميل قاعدة البيانات
            await this.createOrLoadDatabase();
            
            // إنشاء الجداول
            await this.createTables();
            
            // ترحيل البيانات من localStorage إذا لزم الأمر
            await this.migrateFromLocalStorage();
            
            // بدء النسخ الاحتياطي التلقائي
            this.startAutoBackup();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة البيانات الهجين v2.0 بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            
            // العودة إلى localStorage في حالة الفشل
            console.log('🔄 العودة إلى نظام localStorage...');
            return false;
        }
    }

    /**
     * تحميل مكتبة SQL.js
     */
    async loadSQLJS() {
        return new Promise((resolve, reject) => {
            if (window.SQL) {
                resolve();
                return;
            }

            // تحميل SQL.js من CDN
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js';
            script.onload = async () => {
                try {
                    // تهيئة SQL.js
                    const SQL = await initSqlJs({
                        locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                    });
                    window.SQL = SQL;
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * إنشاء أو تحميل قاعدة البيانات
     */
    async createOrLoadDatabase() {
        try {
            // محاولة تحميل قاعدة بيانات موجودة
            const savedDb = localStorage.getItem('mangoo_hybrid_v2_db');
            
            if (savedDb) {
                // تحميل قاعدة البيانات الموجودة
                const dbData = new Uint8Array(JSON.parse(savedDb));
                this.db = new window.SQL.Database(dbData);
                console.log('📂 تم تحميل قاعدة البيانات الموجودة');
            } else {
                // إنشاء قاعدة بيانات جديدة
                this.db = new window.SQL.Database();
                console.log('🆕 تم إنشاء قاعدة بيانات جديدة');
            }
        } catch (error) {
            console.error('خطأ في إنشاء/تحميل قاعدة البيانات:', error);
            this.db = new window.SQL.Database();
        }
    }

    /**
     * إنشاء الجداول الأساسية
     */
    async createTables() {
        const tables = [
            // جدول المنتجات المحسن
            `CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                category TEXT NOT NULL,
                price REAL NOT NULL DEFAULT 0,
                cost REAL DEFAULT 0,
                stock INTEGER DEFAULT 0,
                min_stock INTEGER DEFAULT 5,
                max_stock INTEGER DEFAULT 1000,
                barcode TEXT UNIQUE,
                description TEXT,
                image_url TEXT,
                unit TEXT DEFAULT 'قطعة',
                supplier_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                is_featured BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )`,

            // جدول العملاء المحسن
            `CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT UNIQUE,
                email TEXT,
                address TEXT,
                city TEXT,
                type TEXT DEFAULT 'عادي',
                credit_limit REAL DEFAULT 1000,
                credit_days INTEGER DEFAULT 30,
                total_spent REAL DEFAULT 0,
                total_purchases INTEGER DEFAULT 0,
                loyalty_points INTEGER DEFAULT 0,
                total_earned_points INTEGER DEFAULT 0,
                total_redeemed_points INTEGER DEFAULT 0,
                status TEXT DEFAULT 'نشط',
                last_purchase_date DATETIME,
                registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                birth_date DATE,
                gender TEXT,
                notes TEXT,
                discount_percentage REAL DEFAULT 0
            )`,

            // جدول المبيعات المحسن
            `CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                customer_name TEXT,
                total_amount REAL NOT NULL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                discount_percentage REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                tax_percentage REAL DEFAULT 0,
                final_amount REAL NOT NULL DEFAULT 0,
                payment_method TEXT NOT NULL DEFAULT 'نقدي',
                payment_status TEXT DEFAULT 'مدفوع',
                due_date DATETIME,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                cashier_name TEXT,
                notes TEXT,
                is_refunded BOOLEAN DEFAULT 0,
                refund_amount REAL DEFAULT 0,
                refund_date DATETIME,
                branch_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )`,

            // جدول تفاصيل المبيعات المحسن
            `CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER,
                product_name TEXT NOT NULL,
                quantity REAL NOT NULL DEFAULT 1,
                unit_price REAL NOT NULL DEFAULT 0,
                total_price REAL NOT NULL DEFAULT 0,
                cost_price REAL DEFAULT 0,
                profit REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول الموردين المحسن
            `CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                country TEXT DEFAULT 'فلسطين',
                total_purchases REAL DEFAULT 0,
                last_purchase_date DATETIME,
                status TEXT DEFAULT 'نشط',
                payment_terms TEXT,
                credit_limit REAL DEFAULT 0,
                notes TEXT,
                rating INTEGER DEFAULT 5,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول المشتريات المحسن
            `CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE,
                supplier_id INTEGER,
                supplier_name TEXT,
                total_amount REAL NOT NULL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL NOT NULL DEFAULT 0,
                payment_method TEXT DEFAULT 'نقدي',
                payment_status TEXT DEFAULT 'مدفوع',
                purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                due_date DATETIME,
                received_date DATETIME,
                notes TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )`,

            // جدول تفاصيل المشتريات
            `CREATE TABLE IF NOT EXISTS purchase_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_id INTEGER NOT NULL,
                product_id INTEGER,
                product_name TEXT NOT NULL,
                quantity REAL NOT NULL DEFAULT 1,
                unit_cost REAL NOT NULL DEFAULT 0,
                total_cost REAL NOT NULL DEFAULT 0,
                expiry_date DATE,
                batch_number TEXT,
                FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول حركات المخزون المحسن
            `CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                product_name TEXT NOT NULL,
                movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment', 'transfer'
                quantity REAL NOT NULL,
                old_quantity REAL DEFAULT 0,
                new_quantity REAL DEFAULT 0,
                reference_type TEXT, -- 'sale', 'purchase', 'adjustment', 'production', 'return'
                reference_id INTEGER,
                cost_per_unit REAL DEFAULT 0,
                notes TEXT,
                movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_name TEXT,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )`,

            // جدول نقاط الولاء المحسن
            `CREATE TABLE IF NOT EXISTS loyalty_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL, -- 'earned', 'redeemed', 'expired', 'bonus'
                points INTEGER NOT NULL,
                points_value REAL DEFAULT 0,
                reference_type TEXT, -- 'sale', 'manual', 'bonus', 'birthday', 'promotion'
                reference_id INTEGER,
                description TEXT,
                expiry_date DATE,
                transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )`,

            // جدول الإعدادات المحسن
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT,
                category TEXT DEFAULT 'general',
                is_system BOOLEAN DEFAULT 0,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول الفروع (جديد)
            `CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                address TEXT,
                phone TEXT,
                manager_name TEXT,
                is_main BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول المستخدمين (جديد)
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'cashier', -- 'admin', 'manager', 'cashier'
                branch_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )`
        ];

        // إنشاء الجداول
        for (const tableSQL of tables) {
            try {
                this.db.run(tableSQL);
            } catch (error) {
                console.error('خطأ في إنشاء جدول:', error);
            }
        }

        // إنشاء الفهارس لتحسين الأداء
        this.createIndexes();
        
        // إدراج البيانات الافتراضية
        this.insertDefaultData();
        
        console.log('✅ تم إنشاء جميع الجداول والفهارس');
    }

    /**
     * إنشاء الفهارس لتحسين الأداء
     */
    createIndexes() {
        const indexes = [
            // فهارس المنتجات
            'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
            'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)',
            'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)',
            'CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)',
            'CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock)',
            
            // فهارس العملاء
            'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
            'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)',
            'CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(type)',
            'CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status)',
            
            // فهارس المبيعات
            'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)',
            'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)',
            'CREATE INDEX IF NOT EXISTS idx_sales_invoice ON sales(invoice_number)',
            'CREATE INDEX IF NOT EXISTS idx_sales_payment_method ON sales(payment_method)',
            'CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(payment_status)',
            
            // فهارس تفاصيل المبيعات
            'CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)',
            'CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)',
            'CREATE INDEX IF NOT EXISTS idx_sale_items_name ON sale_items(product_name)',
            
            // فهارس المخزون
            'CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)',
            'CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(movement_date)',
            'CREATE INDEX IF NOT EXISTS idx_inventory_type ON inventory_movements(movement_type)',
            
            // فهارس الولاء
            'CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON loyalty_transactions(customer_id)',
            'CREATE INDEX IF NOT EXISTS idx_loyalty_date ON loyalty_transactions(transaction_date)',
            'CREATE INDEX IF NOT EXISTS idx_loyalty_type ON loyalty_transactions(transaction_type)',
            
            // فهارس الموردين
            'CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)',
            'CREATE INDEX IF NOT EXISTS idx_suppliers_status ON suppliers(status)',
            
            // فهارس المشتريات
            'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date)',
            'CREATE INDEX IF NOT EXISTS idx_purchases_supplier ON purchases(supplier_id)'
        ];

        indexes.forEach(indexSQL => {
            try {
                this.db.run(indexSQL);
            } catch (error) {
                console.error('خطأ في إنشاء فهرس:', error);
            }
        });
    }

    /**
     * إدراج البيانات الافتراضية
     */
    insertDefaultData() {
        try {
            // إعدادات افتراضية
            const defaultSettings = [
                ['company_name', 'محل العصائر والحلويات', 'اسم الشركة'],
                ['tax_rate', '16', 'معدل الضريبة %'],
                ['currency', '₪', 'العملة'],
                ['loyalty_points_rate', '1', 'نقاط الولاء لكل شيكل'],
                ['loyalty_redeem_rate', '0.1', 'قيمة النقطة بالشيكل'],
                ['backup_interval', '30', 'فترة النسخ الاحتياطي بالدقائق'],
                ['low_stock_threshold', '5', 'حد التنبيه للمخزون المنخفض']
            ];

            defaultSettings.forEach(([key, value, description]) => {
                this.db.run(
                    'INSERT OR IGNORE INTO settings (key, value, description, category) VALUES (?, ?, ?, ?)',
                    [key, value, description, 'system']
                );
            });

            // فرع رئيسي افتراضي
            this.db.run(
                'INSERT OR IGNORE INTO branches (name, is_main, is_active) VALUES (?, ?, ?)',
                ['الفرع الرئيسي', 1, 1]
            );

            console.log('✅ تم إدراج البيانات الافتراضية');
        } catch (error) {
            console.error('خطأ في إدراج البيانات الافتراضية:', error);
        }
    }

    /**
     * حفظ قاعدة البيانات في localStorage
     */
    saveDatabase() {
        try {
            if (!this.db) return false;
            
            const data = this.db.export();
            const dataArray = Array.from(data);
            localStorage.setItem('mangoo_hybrid_v2_db', JSON.stringify(dataArray));
            
            // حفظ معلومات النسخة الاحتياطية
            localStorage.setItem('mangoo_hybrid_v2_backup_info', JSON.stringify({
                lastBackup: new Date().toISOString(),
                size: dataArray.length,
                version: this.dbConfig.version,
                recordCount: this.getRecordCount()
            }));
            
            return true;
        } catch (error) {
            console.error('خطأ في حفظ قاعدة البيانات:', error);
            return false;
        }
    }

    /**
     * الحصول على عدد السجلات
     */
    getRecordCount() {
        try {
            const tables = ['products', 'customers', 'sales', 'suppliers'];
            let totalRecords = 0;
            
            tables.forEach(table => {
                const result = this.db.exec(`SELECT COUNT(*) as count FROM ${table}`);
                if (result.length > 0) {
                    totalRecords += result[0].values[0][0];
                }
            });
            
            return totalRecords;
        } catch (error) {
            return 0;
        }
    }

    /**
     * بدء النسخ الاحتياطي التلقائي
     */
    startAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
        }

        if (this.dbConfig.autoBackup) {
            this.backupInterval = setInterval(() => {
                this.saveDatabase();
                console.log('💾 تم إنشاء نسخة احتياطية تلقائية');
            }, this.dbConfig.backupInterval);
        }
    }

    /**
     * إيقاف النسخ الاحتياطي التلقائي
     */
    stopAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
    }

    /**
     * تنفيذ استعلام SQL
     */
    query(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            const result = stmt.getAsObject(params);
            stmt.free();
            
            return result;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }

    /**
     * تنفيذ استعلام وإرجاع جميع النتائج
     */
    queryAll(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            const results = [];
            
            while (stmt.step()) {
                results.push(stmt.getAsObject());
            }
            
            stmt.free();
            return results;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }

    /**
     * تنفيذ استعلام تعديل (INSERT, UPDATE, DELETE)
     */
    run(sql, params = []) {
        try {
            if (!this.isInitialized || !this.db) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stmt = this.db.prepare(sql);
            stmt.run(params);
            stmt.free();
            
            // حفظ التغييرات
            this.saveDatabase();
            
            return true;
        } catch (error) {
            console.error('خطأ في تنفيذ الاستعلام:', error);
            throw error;
        }
    }

    /**
     * ترحيل البيانات من localStorage
     */
    async migrateFromLocalStorage() {
        // سيتم تنفيذ هذا في data-migration.js
        console.log('🔄 ترحيل البيانات من localStorage...');
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const hybridDB = new HybridDatabase();

// تصدير للاستخدام العام
window.hybridDB = hybridDB;
