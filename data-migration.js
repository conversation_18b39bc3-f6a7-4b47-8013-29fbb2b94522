/**
 * نظام ترحيل البيانات من localStorage إلى قاعدة البيانات الهجين
 * يحافظ على جميع البيانات الموجودة ويحولها للنظام الجديد
 */

class DataMigration {
    constructor() {
        this.migrationStatus = {
            products: false,
            customers: false,
            sales: false,
            suppliers: false,
            settings: false
        };
    }

    /**
     * بدء عملية الترحيل الكاملة
     */
    async migrateAllData() {
        try {
            console.log('🔄 بدء ترحيل البيانات من localStorage...');
            
            // التحقق من وجود بيانات للترحيل
            if (!this.hasDataToMigrate()) {
                console.log('ℹ️ لا توجد بيانات للترحيل');
                return true;
            }

            // عرض تقدم الترحيل للمستخدم
            this.showMigrationProgress();

            // ترحيل البيانات بالتسلسل
            await this.migrateProducts();
            await this.migrateCustomers();
            await this.migrateSuppliers();
            await this.migrateSales();
            await this.migrateSettings();

            // إنشاء نسخة احتياطية من localStorage
            this.backupLocalStorageData();

            // تحديث حالة الترحيل
            this.markMigrationComplete();

            console.log('✅ تم ترحيل جميع البيانات بنجاح');
            this.hideMigrationProgress();
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في ترحيل البيانات:', error);
            this.hideMigrationProgress();
            return false;
        }
    }

    /**
     * التحقق من وجود بيانات للترحيل
     */
    hasDataToMigrate() {
        const keys = ['products', 'customers', 'sales', 'suppliers', 'finishedProducts'];
        return keys.some(key => {
            const data = localStorage.getItem(key);
            return data && JSON.parse(data).length > 0;
        });
    }

    /**
     * ترحيل المنتجات
     */
    async migrateProducts() {
        try {
            console.log('📦 ترحيل المنتجات...');
            this.updateMigrationProgress('products', 'جاري ترحيل المنتجات...');

            // ترحيل المواد الخام
            const rawMaterials = JSON.parse(localStorage.getItem('products') || '[]');
            for (const product of rawMaterials) {
                await this.migrateProduct(product, 'مواد خام');
            }

            // ترحيل المنتجات المصنعة
            const finishedProducts = JSON.parse(localStorage.getItem('finishedProducts') || '[]');
            for (const product of finishedProducts) {
                await this.migrateProduct(product, 'منتجات مصنعة');
            }

            this.migrationStatus.products = true;
            this.updateMigrationProgress('products', '✅ تم ترحيل المنتجات');
            console.log('✅ تم ترحيل المنتجات بنجاح');
        } catch (error) {
            console.error('خطأ في ترحيل المنتجات:', error);
            throw error;
        }
    }

    /**
     * ترحيل منتج واحد
     */
    async migrateProduct(product, category) {
        try {
            const sql = `
                INSERT OR IGNORE INTO products 
                (name, category, price, cost, stock, min_stock, barcode, description, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                product.name,
                category,
                product.price || product.sellingPrice || 0,
                product.cost || product.costPrice || 0,
                product.stock || product.quantity || 0,
                product.minStock || 5,
                product.barcode || null,
                product.description || '',
                product.createdAt || new Date().toISOString()
            ]);

            // إضافة حركة مخزون أولية إذا كان هناك مخزون
            if (product.stock > 0 || product.quantity > 0) {
                const quantity = product.stock || product.quantity || 0;
                if (quantity > 0) {
                    const movementSQL = `
                        INSERT INTO inventory_movements 
                        (product_name, movement_type, quantity, reference_type, notes, movement_date)
                        VALUES (?, 'in', ?, 'migration', 'ترحيل من النظام القديم', ?)
                    `;
                    
                    hybridDB.run(movementSQL, [
                        product.name,
                        quantity,
                        product.createdAt || new Date().toISOString()
                    ]);
                }
            }
        } catch (error) {
            console.error(`خطأ في ترحيل المنتج ${product.name}:`, error);
        }
    }

    /**
     * ترحيل العملاء
     */
    async migrateCustomers() {
        try {
            console.log('👥 ترحيل العملاء...');
            this.updateMigrationProgress('customers', 'جاري ترحيل العملاء...');

            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            
            for (const customer of customers) {
                await this.migrateCustomer(customer);
            }

            this.migrationStatus.customers = true;
            this.updateMigrationProgress('customers', '✅ تم ترحيل العملاء');
            console.log('✅ تم ترحيل العملاء بنجاح');
        } catch (error) {
            console.error('خطأ في ترحيل العملاء:', error);
            throw error;
        }
    }

    /**
     * ترحيل عميل واحد
     */
    async migrateCustomer(customer) {
        try {
            const sql = `
                INSERT OR IGNORE INTO customers 
                (name, phone, email, address, type, credit_limit, credit_days, 
                 total_spent, total_purchases, loyalty_points, total_earned_points, 
                 total_redeemed_points, status, last_purchase_date, registration_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                customer.name,
                customer.phone || null,
                customer.email || null,
                customer.address || '',
                customer.type || 'عادي',
                customer.creditLimit || 1000,
                customer.creditDays || 30,
                customer.totalSpent || 0,
                customer.totalPurchases || customer.purchaseCount || 0,
                customer.loyaltyPoints || 0,
                customer.totalEarnedPoints || 0,
                customer.totalRedeemedPoints || 0,
                customer.status || 'نشط',
                customer.lastPurchase || customer.lastPurchaseDate || null,
                customer.registrationDate || new Date().toISOString(),
                customer.notes || ''
            ]);

            // ترحيل تاريخ نقاط الولاء إذا كان موجوداً
            if (customer.pointsHistory && customer.pointsHistory.length > 0) {
                for (const pointTransaction of customer.pointsHistory) {
                    await this.migrateLoyaltyTransaction(customer.name, pointTransaction);
                }
            }
        } catch (error) {
            console.error(`خطأ في ترحيل العميل ${customer.name}:`, error);
        }
    }

    /**
     * ترحيل معاملة نقاط ولاء
     */
    async migrateLoyaltyTransaction(customerName, transaction) {
        try {
            // الحصول على معرف العميل
            const customer = hybridDB.query(
                'SELECT id FROM customers WHERE name = ?',
                [customerName]
            );

            if (!customer) return;

            const sql = `
                INSERT INTO loyalty_transactions 
                (customer_id, transaction_type, points, reference_type, description, transaction_date)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                customer.id,
                transaction.type || 'earned',
                transaction.points || 0,
                transaction.source || 'migration',
                transaction.description || transaction.reason || 'ترحيل من النظام القديم',
                transaction.date || new Date().toISOString()
            ]);
        } catch (error) {
            console.error('خطأ في ترحيل معاملة نقاط الولاء:', error);
        }
    }

    /**
     * ترحيل الموردين
     */
    async migrateSuppliers() {
        try {
            console.log('🏪 ترحيل الموردين...');
            this.updateMigrationProgress('suppliers', 'جاري ترحيل الموردين...');

            const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
            
            for (const supplier of suppliers) {
                await this.migrateSupplier(supplier);
            }

            this.migrationStatus.suppliers = true;
            this.updateMigrationProgress('suppliers', '✅ تم ترحيل الموردين');
            console.log('✅ تم ترحيل الموردين بنجاح');
        } catch (error) {
            console.error('خطأ في ترحيل الموردين:', error);
            throw error;
        }
    }

    /**
     * ترحيل مورد واحد
     */
    async migrateSupplier(supplier) {
        try {
            const sql = `
                INSERT OR IGNORE INTO suppliers 
                (name, contact_person, phone, email, address, total_purchases, 
                 last_purchase_date, status, payment_terms, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                supplier.name,
                supplier.contactPerson || supplier.contact || '',
                supplier.phone || null,
                supplier.email || null,
                supplier.address || '',
                supplier.totalPurchases || 0,
                supplier.lastPurchaseDate || null,
                supplier.status || 'نشط',
                supplier.paymentTerms || '',
                supplier.notes || '',
                supplier.createdAt || new Date().toISOString()
            ]);
        } catch (error) {
            console.error(`خطأ في ترحيل المورد ${supplier.name}:`, error);
        }
    }

    /**
     * ترحيل المبيعات
     */
    async migrateSales() {
        try {
            console.log('💰 ترحيل المبيعات...');
            this.updateMigrationProgress('sales', 'جاري ترحيل المبيعات...');

            const sales = JSON.parse(localStorage.getItem('sales') || '[]');
            
            for (const sale of sales) {
                await this.migrateSale(sale);
            }

            this.migrationStatus.sales = true;
            this.updateMigrationProgress('sales', '✅ تم ترحيل المبيعات');
            console.log('✅ تم ترحيل المبيعات بنجاح');
        } catch (error) {
            console.error('خطأ في ترحيل المبيعات:', error);
            throw error;
        }
    }

    /**
     * ترحيل عملية بيع واحدة
     */
    async migrateSale(sale) {
        try {
            // البحث عن العميل
            let customerId = null;
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                const customer = hybridDB.query(
                    'SELECT id FROM customers WHERE name = ?',
                    [sale.customerName]
                );
                customerId = customer ? customer.id : null;
            }

            // إضافة المبيعة
            const saleSQL = `
                INSERT OR IGNORE INTO sales 
                (invoice_number, customer_id, customer_name, total_amount, discount_amount, 
                 tax_amount, final_amount, payment_method, payment_status, due_date, 
                 sale_date, cashier_name, notes, is_refunded)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(saleSQL, [
                sale.invoiceNumber || sale.id || `INV-${Date.now()}`,
                customerId,
                sale.customerName || 'عميل نقدي',
                sale.subtotal || sale.total || 0,
                sale.discount || 0,
                sale.tax || 0,
                sale.total || sale.finalAmount || 0,
                sale.paymentMethod || 'نقدي',
                sale.paymentMethod === 'آجل' ? 'غير مدفوع' : 'مدفوع',
                sale.dueDate || null,
                sale.date || sale.saleDate || new Date().toISOString(),
                sale.cashier || 'النظام',
                sale.notes || '',
                sale.isRefunded || false
            ]);

            // الحصول على معرف المبيعة
            const saleId = hybridDB.query('SELECT last_insert_rowid() as id').id;

            // ترحيل عناصر المبيعة
            if (sale.items && sale.items.length > 0) {
                for (const item of sale.items) {
                    await this.migrateSaleItem(saleId, item);
                }
            }
        } catch (error) {
            console.error(`خطأ في ترحيل المبيعة ${sale.invoiceNumber}:`, error);
        }
    }

    /**
     * ترحيل عنصر مبيعة
     */
    async migrateSaleItem(saleId, item) {
        try {
            // البحث عن المنتج
            let productId = null;
            const product = hybridDB.query(
                'SELECT id, cost FROM products WHERE name = ?',
                [item.name]
            );
            
            if (product) {
                productId = product.id;
            }

            const costPrice = product ? product.cost : 0;
            const totalPrice = item.price * item.quantity;
            const profit = totalPrice - (costPrice * item.quantity);

            const sql = `
                INSERT INTO sale_items 
                (sale_id, product_id, product_name, quantity, unit_price, total_price, cost_price, profit)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                saleId,
                productId,
                item.name,
                item.quantity,
                item.price,
                totalPrice,
                costPrice,
                profit
            ]);
        } catch (error) {
            console.error(`خطأ في ترحيل عنصر المبيعة ${item.name}:`, error);
        }
    }

    /**
     * ترحيل الإعدادات
     */
    async migrateSettings() {
        try {
            console.log('⚙️ ترحيل الإعدادات...');
            this.updateMigrationProgress('settings', 'جاري ترحيل الإعدادات...');

            // ترحيل إعدادات نظام الولاء
            const loyaltySettings = localStorage.getItem('loyaltySettings');
            if (loyaltySettings) {
                hybridDB.run(
                    'INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)',
                    ['loyaltySettings', loyaltySettings, 'إعدادات نظام الولاء']
                );
            }

            // ترحيل إعدادات أخرى
            const settingsKeys = ['taxRate', 'companyInfo', 'printSettings', 'backupSettings'];
            for (const key of settingsKeys) {
                const value = localStorage.getItem(key);
                if (value) {
                    hybridDB.run(
                        'INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)',
                        [key, value, `إعدادات ${key}`]
                    );
                }
            }

            this.migrationStatus.settings = true;
            this.updateMigrationProgress('settings', '✅ تم ترحيل الإعدادات');
            console.log('✅ تم ترحيل الإعدادات بنجاح');
        } catch (error) {
            console.error('خطأ في ترحيل الإعدادات:', error);
            throw error;
        }
    }

    /**
     * إنشاء نسخة احتياطية من localStorage
     */
    backupLocalStorageData() {
        try {
            const backup = {};
            const keys = ['products', 'customers', 'sales', 'suppliers', 'finishedProducts'];
            
            for (const key of keys) {
                const data = localStorage.getItem(key);
                if (data) {
                    backup[key] = data;
                }
            }

            localStorage.setItem('localStorage_backup', JSON.stringify({
                data: backup,
                backupDate: new Date().toISOString(),
                version: 'pre-hybrid'
            }));

            console.log('💾 تم إنشاء نسخة احتياطية من localStorage');
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        }
    }

    /**
     * تحديد الترحيل كمكتمل
     */
    markMigrationComplete() {
        localStorage.setItem('hybridMigrationComplete', JSON.stringify({
            completed: true,
            date: new Date().toISOString(),
            status: this.migrationStatus
        }));
    }

    /**
     * التحقق من اكتمال الترحيل
     */
    isMigrationComplete() {
        const status = localStorage.getItem('hybridMigrationComplete');
        return status ? JSON.parse(status).completed : false;
    }

    /**
     * عرض تقدم الترحيل
     */
    showMigrationProgress() {
        // سيتم تنفيذ هذا في الواجهة
        console.log('📊 عرض تقدم الترحيل...');
    }

    /**
     * تحديث تقدم الترحيل
     */
    updateMigrationProgress(step, message) {
        console.log(`📊 ${step}: ${message}`);
    }

    /**
     * إخفاء تقدم الترحيل
     */
    hideMigrationProgress() {
        console.log('✅ إخفاء تقدم الترحيل');
    }
}

// إنشاء مثيل من نظام الترحيل
const dataMigration = new DataMigration();

// تصدير للاستخدام العام
window.dataMigration = dataMigration;
