/**
 * نظام التقارير المتقدم للنسخة الهجين v2.0
 * يستفيد من قوة SQL لإنتاج تقارير معقدة وسريعة
 */

class AdvancedReports {
    constructor() {
        this.db = dbAPI;
        this.cache = new Map();
        this.cacheTimeout = 2 * 60 * 1000; // دقيقتان
    }

    /**
     * مسح ذاكرة التخزين المؤقت للتقارير
     */
    clearReportsCache() {
        this.cache.clear();
    }

    /**
     * الحصول من ذاكرة التخزين المؤقت
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    /**
     * حفظ في ذاكرة التخزين المؤقت
     */
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    // ==================== تقارير المبيعات المتقدمة ====================

    /**
     * تقرير المبيعات الشامل
     */
    async generateAdvancedSalesReport(startDate, endDate) {
        try {
            const cacheKey = `sales_report_${startDate}_${endDate}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) return cached;

            if (this.db.fallbackToLocalStorage) {
                return this.generateSalesReportFallback(startDate, endDate);
            }

            const report = {
                summary: await this.getSalesSummary(startDate, endDate),
                dailyBreakdown: await this.getDailySalesBreakdown(startDate, endDate),
                topProducts: await this.getTopSellingProducts(startDate, endDate),
                topCustomers: await this.getTopCustomers(startDate, endDate),
                paymentMethods: await this.getPaymentMethodsBreakdown(startDate, endDate),
                hourlyAnalysis: await this.getHourlySalesAnalysis(startDate, endDate),
                profitAnalysis: await this.getProfitAnalysis(startDate, endDate),
                categoryAnalysis: await this.getCategoryAnalysis(startDate, endDate)
            };

            this.setCache(cacheKey, report);
            return report;
        } catch (error) {
            console.error('خطأ في تقرير المبيعات المتقدم:', error);
            return this.generateSalesReportFallback(startDate, endDate);
        }
    }

    /**
     * ملخص المبيعات المحسن
     */
    async getSalesSummary(startDate, endDate) {
        const sql = `
            SELECT
                COUNT(*) as total_transactions,
                SUM(final_amount) as total_revenue,
                SUM(discount_amount) as total_discounts,
                SUM(tax_amount) as total_tax,
                AVG(final_amount) as average_transaction,
                MIN(final_amount) as min_transaction,
                MAX(final_amount) as max_transaction,
                COUNT(DISTINCT customer_id) as unique_customers,
                SUM(CASE WHEN payment_method = 'نقدي' THEN final_amount ELSE 0 END) as cash_sales,
                SUM(CASE WHEN payment_method = 'بطاقة ائتمان' THEN final_amount ELSE 0 END) as card_sales,
                SUM(CASE WHEN payment_method = 'آجل' THEN final_amount ELSE 0 END) as credit_sales,
                COUNT(CASE WHEN is_refunded = 1 THEN 1 END) as refunded_transactions,
                SUM(CASE WHEN is_refunded = 1 THEN refund_amount ELSE 0 END) as total_refunds
            FROM sales
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
        `;

        return this.db.db.query(sql, [startDate, endDate]);
    }

    /**
     * تحليل المبيعات اليومي المحسن
     */
    async getDailySalesBreakdown(startDate, endDate) {
        const sql = `
            SELECT
                DATE(sale_date) as date,
                strftime('%w', sale_date) as day_of_week,
                COUNT(*) as transactions,
                SUM(final_amount) as revenue,
                SUM(discount_amount) as discounts,
                AVG(final_amount) as avg_transaction,
                COUNT(DISTINCT customer_id) as unique_customers,
                MAX(final_amount) as highest_sale,
                MIN(final_amount) as lowest_sale
            FROM sales
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY DATE(sale_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * أفضل المنتجات مبيعاً مع تحليل متقدم
     */
    async getTopSellingProducts(startDate, endDate, limit = 20) {
        const sql = `
            SELECT
                si.product_name,
                p.category,
                SUM(si.quantity) as total_quantity,
                SUM(si.total_price) as total_revenue,
                SUM(si.profit) as total_profit,
                COUNT(DISTINCT si.sale_id) as times_sold,
                AVG(si.unit_price) as avg_price,
                (SUM(si.profit) / NULLIF(SUM(si.total_price), 0) * 100) as profit_margin,
                (SUM(si.quantity) / COUNT(DISTINCT DATE(s.sale_date))) as daily_avg_quantity,
                p.stock as current_stock,
                p.min_stock
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            LEFT JOIN products p ON si.product_name = p.name
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY si.product_name
            ORDER BY total_quantity DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, limit]);
    }

    /**
     * أفضل العملاء مع تحليل متقدم
     */
    async getTopCustomers(startDate, endDate, limit = 15) {
        const sql = `
            SELECT
                s.customer_name,
                c.phone,
                c.type,
                COUNT(*) as total_purchases,
                SUM(s.final_amount) as total_spent,
                AVG(s.final_amount) as avg_purchase,
                MAX(s.sale_date) as last_purchase,
                SUM(s.discount_amount) as total_discounts_received,
                c.loyalty_points as current_loyalty_points,
                (SUM(s.final_amount) / COUNT(*)) as purchase_frequency_value,
                COUNT(DISTINCT DATE(s.sale_date)) as active_days
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
                AND s.customer_name != 'عميل نقدي'
            GROUP BY s.customer_name
            ORDER BY total_spent DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, limit]);
    }

    /**
     * تحليل طرق الدفع المحسن
     */
    async getPaymentMethodsBreakdown(startDate, endDate) {
        const sql = `
            SELECT
                payment_method,
                COUNT(*) as transactions,
                SUM(final_amount) as total_amount,
                AVG(final_amount) as avg_amount,
                (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sales WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?))) as percentage,
                SUM(CASE WHEN payment_status = 'مدفوع' THEN final_amount ELSE 0 END) as paid_amount,
                SUM(CASE WHEN payment_status = 'غير مدفوع' THEN final_amount ELSE 0 END) as unpaid_amount
            FROM sales
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY payment_method
            ORDER BY total_amount DESC
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, startDate, endDate]);
    }

    /**
     * تحليل المبيعات بالساعة
     */
    async getHourlySalesAnalysis(startDate, endDate) {
        const sql = `
            SELECT
                strftime('%H', sale_date) as hour,
                COUNT(*) as transactions,
                SUM(final_amount) as revenue,
                AVG(final_amount) as avg_transaction,
                COUNT(DISTINCT customer_id) as unique_customers
            FROM sales
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY strftime('%H', sale_date)
            ORDER BY hour
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * تحليل الأرباح المتقدم
     */
    async getProfitAnalysis(startDate, endDate) {
        const sql = `
            SELECT
                DATE(s.sale_date) as date,
                SUM(si.total_price) as revenue,
                SUM(si.cost_price * si.quantity) as cost,
                SUM(si.profit) as profit,
                (SUM(si.profit) / NULLIF(SUM(si.total_price), 0) * 100) as profit_margin,
                COUNT(DISTINCT s.id) as transactions,
                AVG(si.profit) as avg_profit_per_item
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY DATE(s.sale_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * تحليل الفئات
     */
    async getCategoryAnalysis(startDate, endDate) {
        const sql = `
            SELECT
                p.category,
                COUNT(DISTINCT si.product_name) as products_sold,
                SUM(si.quantity) as total_quantity,
                SUM(si.total_price) as total_revenue,
                SUM(si.profit) as total_profit,
                AVG(si.unit_price) as avg_price,
                (SUM(si.profit) / NULLIF(SUM(si.total_price), 0) * 100) as profit_margin
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            LEFT JOIN products p ON si.product_name = p.name
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
                AND p.category IS NOT NULL
            GROUP BY p.category
            ORDER BY total_revenue DESC
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    // ==================== تقارير العملاء المتقدمة ====================

    /**
     * تقرير العملاء الشامل
     */
    async generateAdvancedCustomersReport() {
        try {
            const cacheKey = 'customers_report_advanced';
            const cached = this.getFromCache(cacheKey);
            if (cached) return cached;

            if (this.db.fallbackToLocalStorage) {
                return this.generateCustomersReportFallback();
            }

            const report = {
                summary: await this.getCustomersSummary(),
                segmentation: await this.getCustomerSegmentation(),
                loyaltyAnalysis: await this.getLoyaltyAnalysis(),
                inactiveCustomers: await this.getInactiveCustomers(),
                topSpenders: await this.getTopSpendingCustomers(),
                newCustomers: await this.getNewCustomersAnalysis(),
                birthdayCustomers: await this.getBirthdayCustomers(),
                customerRetention: await this.getCustomerRetentionAnalysis()
            };

            this.setCache(cacheKey, report);
            return report;
        } catch (error) {
            console.error('خطأ في تقرير العملاء المتقدم:', error);
            return this.generateCustomersReportFallback();
        }
    }

    /**
     * ملخص العملاء المحسن
     */
    async getCustomersSummary() {
        const sql = `
            SELECT
                COUNT(*) as total_customers,
                COUNT(CASE WHEN status = 'نشط' THEN 1 END) as active_customers,
                COUNT(CASE WHEN type = 'VIP' THEN 1 END) as vip_customers,
                COUNT(CASE WHEN type = 'مميز' THEN 1 END) as premium_customers,
                SUM(total_spent) as total_customer_spending,
                AVG(total_spent) as avg_customer_spending,
                SUM(loyalty_points) as total_loyalty_points,
                COUNT(CASE WHEN last_purchase_date >= DATE('now', '-30 days') THEN 1 END) as active_last_month,
                COUNT(CASE WHEN last_purchase_date >= DATE('now', '-7 days') THEN 1 END) as active_last_week,
                COUNT(CASE WHEN registration_date >= DATE('now', '-30 days') THEN 1 END) as new_customers_month,
                AVG(total_purchases) as avg_purchases_per_customer
            FROM customers
        `;

        return this.db.db.query(sql);
    }

    /**
     * تجزئة العملاء المحسنة
     */
    async getCustomerSegmentation() {
        const sql = `
            SELECT
                CASE
                    WHEN total_spent >= 10000 THEN 'عملاء VIP+'
                    WHEN total_spent >= 5000 THEN 'عملاء VIP'
                    WHEN total_spent >= 2000 THEN 'عملاء مميزين'
                    WHEN total_spent >= 500 THEN 'عملاء عاديين'
                    WHEN total_spent > 0 THEN 'عملاء جدد'
                    ELSE 'عملاء غير نشطين'
                END as segment,
                COUNT(*) as customer_count,
                SUM(total_spent) as segment_revenue,
                AVG(total_spent) as avg_spending,
                AVG(loyalty_points) as avg_loyalty_points,
                AVG(total_purchases) as avg_purchases,
                (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customers)) as percentage
            FROM customers
            GROUP BY segment
            ORDER BY segment_revenue DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * تحليل نقاط الولاء المحسن
     */
    async getLoyaltyAnalysis() {
        const sql = `
            SELECT
                c.type as customer_type,
                COUNT(*) as customer_count,
                SUM(c.loyalty_points) as total_points,
                AVG(c.loyalty_points) as avg_points,
                SUM(c.total_earned_points) as total_earned,
                SUM(c.total_redeemed_points) as total_redeemed,
                (SUM(c.total_redeemed_points) * 100.0 / NULLIF(SUM(c.total_earned_points), 0)) as redemption_rate,
                COUNT(CASE WHEN c.loyalty_points > 100 THEN 1 END) as high_points_customers
            FROM customers c
            WHERE c.total_earned_points > 0
            GROUP BY c.type
            ORDER BY total_points DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * العملاء غير النشطين
     */
    async getInactiveCustomers(days = 90) {
        const sql = `
            SELECT
                name,
                phone,
                email,
                type,
                total_spent,
                total_purchases,
                loyalty_points,
                last_purchase_date,
                julianday('now') - julianday(last_purchase_date) as days_since_last_purchase,
                registration_date
            FROM customers
            WHERE (last_purchase_date < DATE('now', '-' || ? || ' days')
                OR last_purchase_date IS NULL)
                AND status = 'نشط'
            ORDER BY total_spent DESC
            LIMIT 50
        `;

        return this.db.db.queryAll(sql, [days]);
    }

    /**
     * أفضل العملاء إنفاقاً
     */
    async getTopSpendingCustomers(limit = 25) {
        const sql = `
            SELECT
                name,
                phone,
                email,
                type,
                total_spent,
                total_purchases,
                loyalty_points,
                last_purchase_date,
                registration_date,
                (total_spent / NULLIF(total_purchases, 0)) as avg_purchase_value,
                julianday('now') - julianday(last_purchase_date) as days_since_last_purchase
            FROM customers
            WHERE total_spent > 0 AND status = 'نشط'
            ORDER BY total_spent DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [limit]);
    }

    /**
     * تحليل العملاء الجدد
     */
    async getNewCustomersAnalysis() {
        const sql = `
            SELECT
                DATE(registration_date) as date,
                COUNT(*) as new_customers,
                SUM(total_spent) as total_spent_by_new,
                AVG(total_spent) as avg_spent_by_new,
                COUNT(CASE WHEN total_purchases > 0 THEN 1 END) as customers_with_purchases
            FROM customers
            WHERE registration_date >= DATE('now', '-30 days')
            GROUP BY DATE(registration_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * عملاء أعياد الميلاد
     */
    async getBirthdayCustomers() {
        const sql = `
            SELECT
                name,
                phone,
                email,
                birth_date,
                total_spent,
                loyalty_points,
                strftime('%m-%d', birth_date) as birthday,
                strftime('%m-%d', 'now') as today
            FROM customers
            WHERE birth_date IS NOT NULL
                AND status = 'نشط'
                AND (
                    strftime('%m-%d', birth_date) = strftime('%m-%d', 'now')
                    OR strftime('%m-%d', birth_date) = strftime('%m-%d', DATE('now', '+1 day'))
                    OR strftime('%m-%d', birth_date) = strftime('%m-%d', DATE('now', '+2 days'))
                    OR strftime('%m-%d', birth_date) = strftime('%m-%d', DATE('now', '+3 days'))
                )
            ORDER BY strftime('%m-%d', birth_date)
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * تحليل الاحتفاظ بالعملاء
     */
    async getCustomerRetentionAnalysis() {
        const sql = `
            SELECT
                strftime('%Y-%m', registration_date) as registration_month,
                COUNT(*) as total_registered,
                COUNT(CASE WHEN last_purchase_date >= DATE('now', '-30 days') THEN 1 END) as active_customers,
                (COUNT(CASE WHEN last_purchase_date >= DATE('now', '-30 days') THEN 1 END) * 100.0 / COUNT(*)) as retention_rate,
                AVG(total_spent) as avg_spending,
                AVG(total_purchases) as avg_purchases
            FROM customers
            WHERE registration_date >= DATE('now', '-12 months')
            GROUP BY strftime('%Y-%m', registration_date)
            ORDER BY registration_month DESC
        `;

        return this.db.db.queryAll(sql);
    }

    // ==================== طرق النظام الاحتياطي ====================

    generateSalesReportFallback(startDate, endDate) {
        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
        const filteredSales = sales.filter(sale => {
            const saleDate = new Date(sale.date || sale.saleDate);
            return saleDate >= new Date(startDate) && saleDate <= new Date(endDate);
        });

        return {
            summary: {
                total_transactions: filteredSales.length,
                total_revenue: filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
            },
            message: 'تقرير مبسط - النظام الاحتياطي'
        };
    }

    generateCustomersReportFallback() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        return {
            summary: {
                total_customers: customers.length,
                active_customers: customers.filter(c => c.status !== 'غير نشط').length
            },
            message: 'تقرير مبسط - النظام الاحتياطي'
        };
    }
}

// إنشاء مثيل من نظام التقارير المتقدم
const advancedReports = new AdvancedReports();

// تصدير للاستخدام العام
window.advancedReports = advancedReports;