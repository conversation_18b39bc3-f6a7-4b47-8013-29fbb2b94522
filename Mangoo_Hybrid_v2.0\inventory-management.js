/**
 * نظام إدارة المخزون للنسخة الهجين v2.0
 * محسن للأداء والسرعة
 */

class InventoryManagement {
    constructor() {
        this.isInitialized = false;
        this.currentView = 'products';
    }

    /**
     * تهيئة نظام إدارة المخزون
     */
    async initialize() {
        if (this.isInitialized) return;
        
        console.log('📦 تهيئة نظام إدارة المخزون الهجين...');
        
        // إنشاء واجهة إدارة المخزون
        this.createInventoryInterface();
        
        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام إدارة المخزون');
    }

    /**
     * إنشاء واجهة إدارة المخزون
     */
    createInventoryInterface() {
        const inventoryHTML = `
            <div class="inventory-container" style="display: none;" id="inventoryContainer">
                <div class="inventory-header">
                    <h2>📦 إدارة المخزون الهجين</h2>
                    <button class="btn btn-secondary" onclick="closeInventory()">إغلاق</button>
                </div>
                
                <div class="inventory-nav">
                    <button class="btn btn-primary" onclick="switchInventoryView('products')">المنتجات</button>
                    <button class="btn btn-outline-primary" onclick="switchInventoryView('lowStock')">مخزون منخفض</button>
                    <button class="btn btn-outline-primary" onclick="switchInventoryView('movements')">حركات المخزون</button>
                    <button class="btn btn-success" onclick="showAddProductModal()">إضافة منتج</button>
                </div>
                
                <div class="inventory-content">
                    <!-- عرض المنتجات -->
                    <div id="productsView" class="inventory-view">
                        <div class="search-bar">
                            <input type="text" id="productSearchInput" placeholder="ابحث في المنتجات..." 
                                   onkeyup="searchInventoryProducts(this.value)" class="form-control">
                        </div>
                        <div class="products-table-container">
                            <table class="table" id="productsTable">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>التكلفة</th>
                                        <th>المخزون</th>
                                        <th>الحد الأدنى</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- المنتجات ستظهر هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- عرض المخزون المنخفض -->
                    <div id="lowStockView" class="inventory-view" style="display: none;">
                        <div class="alert alert-warning">
                            <strong>تنبيه:</strong> المنتجات التالية تحتاج إعادة تموين
                        </div>
                        <div class="low-stock-container" id="lowStockContainer">
                            <!-- المنتجات منخفضة المخزون ستظهر هنا -->
                        </div>
                    </div>
                    
                    <!-- عرض حركات المخزون -->
                    <div id="movementsView" class="inventory-view" style="display: none;">
                        <div class="movements-filters">
                            <select id="movementTypeFilter" class="form-control" onchange="filterMovements()">
                                <option value="">جميع الحركات</option>
                                <option value="in">دخول</option>
                                <option value="out">خروج</option>
                                <option value="adjustment">تعديل</option>
                            </select>
                        </div>
                        <div class="movements-container" id="movementsContainer">
                            <!-- حركات المخزون ستظهر هنا -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نافذة إضافة منتج -->
            <div class="modal" id="addProductModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إضافة منتج جديد</h3>
                        <button onclick="closeAddProductModal()" class="btn btn-secondary">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="addProductForm">
                            <div class="form-group">
                                <label>اسم المنتج:</label>
                                <input type="text" id="productName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>الفئة:</label>
                                <select id="productCategory" class="form-control" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="مواد خام">مواد خام</option>
                                    <option value="منتجات مصنعة">منتجات مصنعة</option>
                                    <option value="مشروبات">مشروبات</option>
                                    <option value="حلويات">حلويات</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>السعر:</label>
                                    <input type="number" id="productPrice" class="form-control" step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>التكلفة:</label>
                                    <input type="number" id="productCost" class="form-control" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المخزون الأولي:</label>
                                    <input type="number" id="productStock" class="form-control" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label>الحد الأدنى:</label>
                                    <input type="number" id="productMinStock" class="form-control" min="0" value="5">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>الوحدة:</label>
                                <select id="productUnit" class="form-control">
                                    <option value="قطعة">قطعة</option>
                                    <option value="كيلو">كيلو</option>
                                    <option value="لتر">لتر</option>
                                    <option value="علبة">علبة</option>
                                    <option value="كيس">كيس</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>الوصف:</label>
                                <textarea id="productDescription" class="form-control" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button onclick="saveProduct()" class="btn btn-success">حفظ</button>
                        <button onclick="closeAddProductModal()" class="btn btn-secondary">إلغاء</button>
                    </div>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const inventoryStyles = `
            <style>
                .inventory-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: white;
                    z-index: 10000;
                    overflow-y: auto;
                }
                
                .inventory-header {
                    background: #2c3e50;
                    color: white;
                    padding: 1rem 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .inventory-nav {
                    background: #34495e;
                    padding: 1rem 2rem;
                    display: flex;
                    gap: 1rem;
                }
                
                .inventory-content {
                    padding: 2rem;
                }
                
                .inventory-view {
                    min-height: 500px;
                }
                
                .search-bar {
                    margin-bottom: 1rem;
                }
                
                .products-table-container {
                    max-height: 600px;
                    overflow-y: auto;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                
                .table {
                    margin: 0;
                }
                
                .table th {
                    background: #f8f9fa;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                }
                
                .stock-status {
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.8rem;
                    font-weight: bold;
                }
                
                .stock-good {
                    background: #d4edda;
                    color: #155724;
                }
                
                .stock-low {
                    background: #fff3cd;
                    color: #856404;
                }
                
                .stock-out {
                    background: #f8d7da;
                    color: #721c24;
                }
                
                .low-stock-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 1rem;
                }
                
                .low-stock-card {
                    background: white;
                    border: 1px solid #ffc107;
                    border-radius: 8px;
                    padding: 1rem;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                
                .movements-filters {
                    margin-bottom: 1rem;
                    display: flex;
                    gap: 1rem;
                }
                
                .movements-container {
                    max-height: 600px;
                    overflow-y: auto;
                }
                
                .movement-item {
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 1rem;
                    margin-bottom: 0.5rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .movement-in {
                    border-left: 4px solid #28a745;
                }
                
                .movement-out {
                    border-left: 4px solid #dc3545;
                }
                
                .movement-adjustment {
                    border-left: 4px solid #ffc107;
                }
                
                .modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 20000;
                }
                
                .modal-content {
                    background: white;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 600px;
                    max-height: 90vh;
                    overflow-y: auto;
                }
                
                .modal-header {
                    background: #f8f9fa;
                    padding: 1rem;
                    border-bottom: 1px solid #ddd;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .modal-body {
                    padding: 1rem;
                }
                
                .modal-footer {
                    background: #f8f9fa;
                    padding: 1rem;
                    border-top: 1px solid #ddd;
                    display: flex;
                    justify-content: flex-end;
                    gap: 1rem;
                }
                
                .form-row {
                    display: flex;
                    gap: 1rem;
                }
                
                .form-row .form-group {
                    flex: 1;
                }
                
                .form-group {
                    margin-bottom: 1rem;
                }
                
                .form-group label {
                    display: block;
                    margin-bottom: 0.25rem;
                    font-weight: bold;
                }
                
                .form-control {
                    width: 100%;
                    padding: 0.5rem;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 1rem;
                }
                
                .btn {
                    padding: 0.5rem 1rem;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 0.9rem;
                    text-decoration: none;
                    display: inline-block;
                    text-align: center;
                }
                
                .btn-primary {
                    background: #007bff;
                    color: white;
                }
                
                .btn-outline-primary {
                    background: transparent;
                    color: #007bff;
                    border: 1px solid #007bff;
                }
                
                .btn-success {
                    background: #28a745;
                    color: white;
                }
                
                .btn-warning {
                    background: #ffc107;
                    color: #212529;
                }
                
                .btn-danger {
                    background: #dc3545;
                    color: white;
                }
                
                .btn-secondary {
                    background: #6c757d;
                    color: white;
                }
                
                .btn-sm {
                    padding: 0.25rem 0.5rem;
                    font-size: 0.8rem;
                }
                
                .alert {
                    padding: 1rem;
                    border-radius: 4px;
                    margin-bottom: 1rem;
                }
                
                .alert-warning {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                }
            </style>
        `;

        // إضافة الواجهة والأنماط إلى الصفحة
        document.head.insertAdjacentHTML('beforeend', inventoryStyles);
        document.body.insertAdjacentHTML('beforeend', inventoryHTML);
    }

    /**
     * فتح إدارة المخزون
     */
    async openInventory() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        document.getElementById('inventoryContainer').style.display = 'block';
        await this.loadProducts();
    }

    /**
     * إغلاق إدارة المخزون
     */
    closeInventory() {
        document.getElementById('inventoryContainer').style.display = 'none';
    }

    /**
     * تبديل العرض
     */
    switchView(view) {
        // إخفاء جميع العروض
        document.querySelectorAll('.inventory-view').forEach(v => v.style.display = 'none');
        
        // إظهار العرض المطلوب
        document.getElementById(view + 'View').style.display = 'block';
        
        // تحديث أزرار التنقل
        document.querySelectorAll('.inventory-nav .btn').forEach(btn => {
            btn.className = btn.className.replace('btn-primary', 'btn-outline-primary');
        });
        
        event.target.className = event.target.className.replace('btn-outline-primary', 'btn-primary');
        
        this.currentView = view;
        
        // تحميل البيانات حسب العرض
        switch(view) {
            case 'products':
                this.loadProducts();
                break;
            case 'lowStock':
                this.loadLowStockProducts();
                break;
            case 'movements':
                this.loadMovements();
                break;
        }
    }

    /**
     * تحميل المنتجات
     */
    async loadProducts() {
        try {
            const products = dbAPI.getProducts(true);
            this.displayProducts(products);
        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
        }
    }

    /**
     * عرض المنتجات
     */
    displayProducts(products) {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        tbody.innerHTML = products.map(product => {
            const stockStatus = this.getStockStatus(product.stock, product.min_stock);
            return `
                <tr>
                    <td><strong>${product.name}</strong></td>
                    <td>${product.category}</td>
                    <td>${product.price.toFixed(2)} ₪</td>
                    <td>${(product.cost || 0).toFixed(2)} ₪</td>
                    <td>${product.stock}</td>
                    <td>${product.min_stock}</td>
                    <td><span class="stock-status ${stockStatus.class}">${stockStatus.text}</span></td>
                    <td>
                        <button onclick="editProductStock('${product.name}', ${product.stock})" class="btn btn-sm btn-warning">تعديل المخزون</button>
                        <button onclick="editProduct(${product.id})" class="btn btn-sm btn-primary">تعديل</button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * الحصول على حالة المخزون
     */
    getStockStatus(stock, minStock) {
        if (stock <= 0) {
            return { class: 'stock-out', text: 'نفد' };
        } else if (stock <= minStock) {
            return { class: 'stock-low', text: 'منخفض' };
        } else {
            return { class: 'stock-good', text: 'جيد' };
        }
    }

    /**
     * تحميل المنتجات منخفضة المخزون
     */
    async loadLowStockProducts() {
        try {
            const products = dbAPI.getLowStockProducts();
            this.displayLowStockProducts(products);
        } catch (error) {
            console.error('خطأ في تحميل المنتجات منخفضة المخزون:', error);
        }
    }

    /**
     * عرض المنتجات منخفضة المخزون
     */
    displayLowStockProducts(products) {
        const container = document.getElementById('lowStockContainer');
        if (!container) return;

        if (products.length === 0) {
            container.innerHTML = '<div class="alert alert-success">جميع المنتجات لديها مخزون كافي</div>';
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="low-stock-card">
                <h4>${product.name}</h4>
                <p><strong>الفئة:</strong> ${product.category}</p>
                <p><strong>المخزون الحالي:</strong> ${product.stock}</p>
                <p><strong>الحد الأدنى:</strong> ${product.min_stock}</p>
                <p><strong>المطلوب:</strong> ${product.min_stock - product.stock + 10} وحدة</p>
                <button onclick="editProductStock('${product.name}', ${product.stock})" class="btn btn-warning">إعادة تموين</button>
            </div>
        `).join('');
    }

    /**
     * تحميل حركات المخزون
     */
    async loadMovements() {
        try {
            // سيتم تنفيذ هذا عند توفر البيانات
            const movements = []; // dbAPI.getInventoryMovements();
            this.displayMovements(movements);
        } catch (error) {
            console.error('خطأ في تحميل حركات المخزون:', error);
        }
    }

    /**
     * عرض حركات المخزون
     */
    displayMovements(movements) {
        const container = document.getElementById('movementsContainer');
        if (!container) return;

        if (movements.length === 0) {
            container.innerHTML = '<div class="alert alert-info">لا توجد حركات مخزون</div>';
            return;
        }

        container.innerHTML = movements.map(movement => `
            <div class="movement-item movement-${movement.movement_type}">
                <div>
                    <strong>${movement.product_name}</strong><br>
                    <small>${movement.notes || movement.reference_type}</small>
                </div>
                <div>
                    <span class="movement-quantity">${movement.movement_type === 'in' ? '+' : '-'}${movement.quantity}</span><br>
                    <small>${new Date(movement.movement_date).toLocaleDateString('ar-SA')}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * حفظ منتج جديد
     */
    async saveProduct() {
        try {
            const product = {
                name: document.getElementById('productName').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                cost: parseFloat(document.getElementById('productCost').value) || 0,
                stock: parseInt(document.getElementById('productStock').value) || 0,
                minStock: parseInt(document.getElementById('productMinStock').value) || 5,
                unit: document.getElementById('productUnit').value,
                description: document.getElementById('productDescription').value
            };

            if (!product.name || !product.category || !product.price) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const success = await dbAPI.addProduct(product);
            
            if (success) {
                alert('تم إضافة المنتج بنجاح');
                this.closeAddProductModal();
                this.loadProducts();
            } else {
                alert('حدث خطأ في إضافة المنتج');
            }
        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            alert('حدث خطأ في حفظ المنتج');
        }
    }

    /**
     * إظهار نافذة إضافة منتج
     */
    showAddProductModal() {
        document.getElementById('addProductModal').style.display = 'flex';
    }

    /**
     * إغلاق نافذة إضافة منتج
     */
    closeAddProductModal() {
        document.getElementById('addProductModal').style.display = 'none';
        document.getElementById('addProductForm').reset();
    }
}

// إنشاء مثيل من نظام إدارة المخزون
const inventoryManagement = new InventoryManagement();

// الوظائف العامة
window.manageInventory = async function() {
    await inventoryManagement.openInventory();
};

window.closeInventory = function() {
    inventoryManagement.closeInventory();
};

window.switchInventoryView = function(view) {
    inventoryManagement.switchView(view);
};

window.showAddProductModal = function() {
    inventoryManagement.showAddProductModal();
};

window.closeAddProductModal = function() {
    inventoryManagement.closeAddProductModal();
};

window.saveProduct = async function() {
    await inventoryManagement.saveProduct();
};

window.searchInventoryProducts = function(searchTerm) {
    if (searchTerm.length < 2) {
        inventoryManagement.loadProducts();
        return;
    }
    
    const products = dbAPI.searchProducts(searchTerm);
    inventoryManagement.displayProducts(products);
};

window.editProductStock = function(productName, currentStock) {
    const newStock = prompt(`تعديل مخزون ${productName}\nالمخزون الحالي: ${currentStock}`, currentStock);
    if (newStock !== null && !isNaN(newStock)) {
        dbAPI.updateProductStock(productName, parseInt(newStock), 'تعديل يدوي');
        inventoryManagement.loadProducts();
    }
};

window.editProduct = function(productId) {
    alert('ميزة تعديل المنتج ستكون متاحة قريباً');
};

window.filterMovements = function() {
    inventoryManagement.loadMovements();
};

// تصدير للاستخدام العام
window.inventoryManagement = inventoryManagement;
