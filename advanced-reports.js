/**
 * نظام التقارير المتقدم للنظام الهجين
 * يستفيد من قوة SQL لإنتاج تقارير معقدة وسريعة
 */

class AdvancedReports {
    constructor() {
        this.db = dbAPI;
    }

    /**
     * تقرير المبيعات المتقدم
     */
    async generateAdvancedSalesReport(startDate, endDate) {
        try {
            if (this.db.fallbackToLocalStorage) {
                return this.generateSalesReportFallback(startDate, endDate);
            }

            const report = {
                summary: await this.getSalesSummary(startDate, endDate),
                dailyBreakdown: await this.getDailySalesBreakdown(startDate, endDate),
                topProducts: await this.getTopSellingProducts(startDate, endDate),
                topCustomers: await this.getTopCustomers(startDate, endDate),
                paymentMethods: await this.getPaymentMethodsBreakdown(startDate, endDate),
                hourlyAnalysis: await this.getHourlySalesAnalysis(startDate, endDate),
                profitAnalysis: await this.getProfitAnalysis(startDate, endDate)
            };

            return report;
        } catch (error) {
            console.error('خطأ في تقرير المبيعات المتقدم:', error);
            return this.generateSalesReportFallback(startDate, endDate);
        }
    }

    /**
     * ملخص المبيعات
     */
    async getSalesSummary(startDate, endDate) {
        const sql = `
            SELECT 
                COUNT(*) as total_transactions,
                SUM(final_amount) as total_revenue,
                SUM(discount_amount) as total_discounts,
                SUM(tax_amount) as total_tax,
                AVG(final_amount) as average_transaction,
                MIN(final_amount) as min_transaction,
                MAX(final_amount) as max_transaction,
                COUNT(DISTINCT customer_id) as unique_customers
            FROM sales 
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
        `;

        return this.db.db.query(sql, [startDate, endDate]);
    }

    /**
     * تحليل المبيعات اليومي
     */
    async getDailySalesBreakdown(startDate, endDate) {
        const sql = `
            SELECT 
                DATE(sale_date) as date,
                COUNT(*) as transactions,
                SUM(final_amount) as revenue,
                SUM(discount_amount) as discounts,
                AVG(final_amount) as avg_transaction,
                COUNT(DISTINCT customer_id) as unique_customers
            FROM sales 
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY DATE(sale_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * أفضل المنتجات مبيعاً
     */
    async getTopSellingProducts(startDate, endDate, limit = 10) {
        const sql = `
            SELECT 
                si.product_name,
                SUM(si.quantity) as total_quantity,
                SUM(si.total_price) as total_revenue,
                SUM(si.profit) as total_profit,
                COUNT(DISTINCT si.sale_id) as times_sold,
                AVG(si.unit_price) as avg_price,
                (SUM(si.profit) / SUM(si.total_price) * 100) as profit_margin
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY si.product_name
            ORDER BY total_quantity DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, limit]);
    }

    /**
     * أفضل العملاء
     */
    async getTopCustomers(startDate, endDate, limit = 10) {
        const sql = `
            SELECT 
                s.customer_name,
                COUNT(*) as total_purchases,
                SUM(s.final_amount) as total_spent,
                AVG(s.final_amount) as avg_purchase,
                MAX(s.sale_date) as last_purchase,
                SUM(s.discount_amount) as total_discounts_received
            FROM sales s
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
                AND s.customer_name != 'عميل نقدي'
            GROUP BY s.customer_name
            ORDER BY total_spent DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, limit]);
    }

    /**
     * تحليل طرق الدفع
     */
    async getPaymentMethodsBreakdown(startDate, endDate) {
        const sql = `
            SELECT 
                payment_method,
                COUNT(*) as transactions,
                SUM(final_amount) as total_amount,
                AVG(final_amount) as avg_amount,
                (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sales WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?))) as percentage
            FROM sales 
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY payment_method
            ORDER BY total_amount DESC
        `;

        return this.db.db.queryAll(sql, [startDate, endDate, startDate, endDate]);
    }

    /**
     * تحليل المبيعات بالساعة
     */
    async getHourlySalesAnalysis(startDate, endDate) {
        const sql = `
            SELECT 
                strftime('%H', sale_date) as hour,
                COUNT(*) as transactions,
                SUM(final_amount) as revenue,
                AVG(final_amount) as avg_transaction
            FROM sales 
            WHERE DATE(sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY strftime('%H', sale_date)
            ORDER BY hour
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * تحليل الأرباح
     */
    async getProfitAnalysis(startDate, endDate) {
        const sql = `
            SELECT 
                DATE(s.sale_date) as date,
                SUM(si.total_price) as revenue,
                SUM(si.cost_price * si.quantity) as cost,
                SUM(si.profit) as profit,
                (SUM(si.profit) / SUM(si.total_price) * 100) as profit_margin
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            WHERE DATE(s.sale_date) BETWEEN DATE(?) AND DATE(?)
            GROUP BY DATE(s.sale_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql, [startDate, endDate]);
    }

    /**
     * تقرير العملاء المتقدم
     */
    async generateAdvancedCustomersReport() {
        try {
            if (this.db.fallbackToLocalStorage) {
                return this.generateCustomersReportFallback();
            }

            const report = {
                summary: await this.getCustomersSummary(),
                segmentation: await this.getCustomerSegmentation(),
                loyaltyAnalysis: await this.getLoyaltyAnalysis(),
                inactiveCustomers: await this.getInactiveCustomers(),
                topSpenders: await this.getTopSpendingCustomers(),
                newCustomers: await this.getNewCustomersAnalysis()
            };

            return report;
        } catch (error) {
            console.error('خطأ في تقرير العملاء المتقدم:', error);
            return this.generateCustomersReportFallback();
        }
    }

    /**
     * ملخص العملاء
     */
    async getCustomersSummary() {
        const sql = `
            SELECT 
                COUNT(*) as total_customers,
                COUNT(CASE WHEN status = 'نشط' THEN 1 END) as active_customers,
                COUNT(CASE WHEN type = 'VIP' THEN 1 END) as vip_customers,
                COUNT(CASE WHEN type = 'مميز' THEN 1 END) as premium_customers,
                SUM(total_spent) as total_customer_spending,
                AVG(total_spent) as avg_customer_spending,
                SUM(loyalty_points) as total_loyalty_points,
                COUNT(CASE WHEN last_purchase_date >= DATE('now', '-30 days') THEN 1 END) as active_last_month
            FROM customers
        `;

        return this.db.db.query(sql);
    }

    /**
     * تجزئة العملاء
     */
    async getCustomerSegmentation() {
        const sql = `
            SELECT 
                CASE 
                    WHEN total_spent >= 5000 THEN 'عملاء VIP'
                    WHEN total_spent >= 2000 THEN 'عملاء مميزين'
                    WHEN total_spent >= 500 THEN 'عملاء عاديين'
                    ELSE 'عملاء جدد'
                END as segment,
                COUNT(*) as customer_count,
                SUM(total_spent) as segment_revenue,
                AVG(total_spent) as avg_spending,
                AVG(loyalty_points) as avg_loyalty_points
            FROM customers
            GROUP BY segment
            ORDER BY segment_revenue DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * تحليل نقاط الولاء
     */
    async getLoyaltyAnalysis() {
        const sql = `
            SELECT 
                c.type as customer_type,
                COUNT(*) as customer_count,
                SUM(c.loyalty_points) as total_points,
                AVG(c.loyalty_points) as avg_points,
                SUM(c.total_earned_points) as total_earned,
                SUM(c.total_redeemed_points) as total_redeemed,
                (SUM(c.total_redeemed_points) * 100.0 / NULLIF(SUM(c.total_earned_points), 0)) as redemption_rate
            FROM customers c
            WHERE c.total_earned_points > 0
            GROUP BY c.type
            ORDER BY total_points DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * العملاء غير النشطين
     */
    async getInactiveCustomers(days = 90) {
        const sql = `
            SELECT 
                name,
                phone,
                total_spent,
                total_purchases,
                last_purchase_date,
                julianday('now') - julianday(last_purchase_date) as days_since_last_purchase
            FROM customers
            WHERE last_purchase_date < DATE('now', '-' || ? || ' days')
                OR last_purchase_date IS NULL
            ORDER BY total_spent DESC
        `;

        return this.db.db.queryAll(sql, [days]);
    }

    /**
     * أفضل العملاء إنفاقاً
     */
    async getTopSpendingCustomers(limit = 20) {
        const sql = `
            SELECT 
                name,
                phone,
                type,
                total_spent,
                total_purchases,
                loyalty_points,
                last_purchase_date,
                (total_spent / NULLIF(total_purchases, 0)) as avg_purchase_value
            FROM customers
            WHERE total_spent > 0
            ORDER BY total_spent DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [limit]);
    }

    /**
     * تحليل العملاء الجدد
     */
    async getNewCustomersAnalysis() {
        const sql = `
            SELECT 
                DATE(registration_date) as date,
                COUNT(*) as new_customers,
                SUM(total_spent) as total_spent_by_new,
                AVG(total_spent) as avg_spent_by_new
            FROM customers
            WHERE registration_date >= DATE('now', '-30 days')
            GROUP BY DATE(registration_date)
            ORDER BY date
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * تقرير المخزون المتقدم
     */
    async generateAdvancedInventoryReport() {
        try {
            if (this.db.fallbackToLocalStorage) {
                return this.generateInventoryReportFallback();
            }

            const report = {
                summary: await this.getInventorySummary(),
                lowStock: await this.getLowStockProducts(),
                fastMoving: await this.getFastMovingProducts(),
                slowMoving: await this.getSlowMovingProducts(),
                stockValue: await this.getStockValueAnalysis(),
                movements: await this.getRecentInventoryMovements()
            };

            return report;
        } catch (error) {
            console.error('خطأ في تقرير المخزون المتقدم:', error);
            return this.generateInventoryReportFallback();
        }
    }

    /**
     * ملخص المخزون
     */
    async getInventorySummary() {
        const sql = `
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN stock <= min_stock THEN 1 END) as low_stock_products,
                COUNT(CASE WHEN stock = 0 THEN 1 END) as out_of_stock_products,
                SUM(stock * cost) as total_stock_value,
                SUM(stock * price) as total_retail_value,
                AVG(stock) as avg_stock_level
            FROM products
            WHERE is_active = 1
        `;

        return this.db.db.query(sql);
    }

    /**
     * المنتجات منخفضة المخزون
     */
    async getLowStockProducts() {
        const sql = `
            SELECT 
                name,
                category,
                stock,
                min_stock,
                (min_stock - stock) as shortage,
                cost,
                price,
                (stock * cost) as current_value
            FROM products
            WHERE stock <= min_stock AND is_active = 1
            ORDER BY shortage DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * المنتجات سريعة الحركة
     */
    async getFastMovingProducts(days = 30) {
        const sql = `
            SELECT 
                p.name,
                p.category,
                p.stock,
                SUM(si.quantity) as sold_quantity,
                (SUM(si.quantity) / ?) as daily_avg_sales,
                (p.stock / NULLIF((SUM(si.quantity) / ?), 0)) as days_of_stock
            FROM products p
            LEFT JOIN sale_items si ON p.name = si.product_name
            LEFT JOIN sales s ON si.sale_id = s.id
            WHERE s.sale_date >= DATE('now', '-' || ? || ' days')
                AND p.is_active = 1
            GROUP BY p.name
            HAVING sold_quantity > 0
            ORDER BY daily_avg_sales DESC
            LIMIT 20
        `;

        return this.db.db.queryAll(sql, [days, days, days]);
    }

    /**
     * المنتجات بطيئة الحركة
     */
    async getSlowMovingProducts(days = 90) {
        const sql = `
            SELECT 
                p.name,
                p.category,
                p.stock,
                p.cost,
                (p.stock * p.cost) as tied_capital,
                COALESCE(SUM(si.quantity), 0) as sold_quantity,
                MAX(s.sale_date) as last_sale_date
            FROM products p
            LEFT JOIN sale_items si ON p.name = si.product_name
            LEFT JOIN sales s ON si.sale_id = s.id AND s.sale_date >= DATE('now', '-' || ? || ' days')
            WHERE p.is_active = 1 AND p.stock > 0
            GROUP BY p.name
            HAVING sold_quantity = 0 OR sold_quantity IS NULL
            ORDER BY tied_capital DESC
        `;

        return this.db.db.queryAll(sql, [days]);
    }

    /**
     * تحليل قيمة المخزون
     */
    async getStockValueAnalysis() {
        const sql = `
            SELECT 
                category,
                COUNT(*) as product_count,
                SUM(stock) as total_quantity,
                SUM(stock * cost) as cost_value,
                SUM(stock * price) as retail_value,
                (SUM(stock * price) - SUM(stock * cost)) as potential_profit
            FROM products
            WHERE is_active = 1 AND stock > 0
            GROUP BY category
            ORDER BY cost_value DESC
        `;

        return this.db.db.queryAll(sql);
    }

    /**
     * حركات المخزون الأخيرة
     */
    async getRecentInventoryMovements(limit = 50) {
        const sql = `
            SELECT 
                product_name,
                movement_type,
                quantity,
                reference_type,
                notes,
                movement_date
            FROM inventory_movements
            ORDER BY movement_date DESC
            LIMIT ?
        `;

        return this.db.db.queryAll(sql, [limit]);
    }

    // ==================== طرق النظام الاحتياطي ====================

    generateSalesReportFallback(startDate, endDate) {
        // تنفيذ مبسط باستخدام localStorage
        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
        // ... تنفيذ مبسط
        return {
            summary: { total_transactions: sales.length },
            message: 'تقرير مبسط - النظام الاحتياطي'
        };
    }

    generateCustomersReportFallback() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        return {
            summary: { total_customers: customers.length },
            message: 'تقرير مبسط - النظام الاحتياطي'
        };
    }

    generateInventoryReportFallback() {
        const products = JSON.parse(localStorage.getItem('products') || '[]');
        return {
            summary: { total_products: products.length },
            message: 'تقرير مبسط - النظام الاحتياطي'
        };
    }
}

// إنشاء مثيل من نظام التقارير المتقدم
const advancedReports = new AdvancedReports();

// تصدير للاستخدام العام
window.advancedReports = advancedReports;
