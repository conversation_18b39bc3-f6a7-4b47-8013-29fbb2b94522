/**
 * نظام ترحيل البيانات للنسخة الهجين v2.0
 * يحول البيانات من localStorage إلى قاعدة البيانات SQLite
 */

class DataMigration {
    constructor() {
        this.migrationStatus = {
            products: false,
            customers: false,
            sales: false,
            suppliers: false,
            settings: false,
            finishedProducts: false
        };
        
        this.migrationProgress = 0;
        this.totalSteps = 0;
        this.currentStep = 0;
    }

    /**
     * بدء عملية الترحيل الكاملة
     */
    async migrateAllData() {
        try {
            console.log('🔄 بدء ترحيل البيانات إلى النظام الهجين v2.0...');
            
            // التحقق من وجود بيانات للترحيل
            if (!this.hasDataToMigrate()) {
                console.log('ℹ️ لا توجد بيانات للترحيل');
                this.markMigrationComplete();
                return true;
            }

            // حساب إجمالي الخطوات
            this.calculateTotalSteps();

            // ترحيل البيانات بالتسلسل
            await this.migrateProducts();
            await this.migrateFinishedProducts();
            await this.migrateCustomers();
            await this.migrateSuppliers();
            await this.migrateSales();
            await this.migrateSettings();

            // إنشاء نسخة احتياطية من localStorage
            this.backupLocalStorageData();

            // تحديث حالة الترحيل
            this.markMigrationComplete();

            console.log('✅ تم ترحيل جميع البيانات بنجاح إلى النظام الهجين v2.0');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في ترحيل البيانات:', error);
            return false;
        }
    }

    /**
     * التحقق من وجود بيانات للترحيل
     */
    hasDataToMigrate() {
        const keys = ['products', 'customers', 'sales', 'suppliers', 'finishedProducts'];
        return keys.some(key => {
            const data = localStorage.getItem(key);
            return data && JSON.parse(data).length > 0;
        });
    }

    /**
     * حساب إجمالي الخطوات
     */
    calculateTotalSteps() {
        const keys = ['products', 'customers', 'sales', 'suppliers', 'finishedProducts'];
        this.totalSteps = 0;
        
        keys.forEach(key => {
            const data = localStorage.getItem(key);
            if (data) {
                this.totalSteps += JSON.parse(data).length;
            }
        });
        
        this.totalSteps += 10; // خطوات إضافية للإعدادات والتنظيف
    }

    /**
     * تحديث تقدم الترحيل
     */
    updateProgress(step, message) {
        this.currentStep++;
        this.migrationProgress = Math.round((this.currentStep / this.totalSteps) * 100);
        console.log(`📊 ${this.migrationProgress}% - ${step}: ${message}`);
        
        // تحديث واجهة المستخدم إذا كانت متاحة
        if (window.updateLoadingStatus) {
            window.updateLoadingStatus(`${message} (${this.migrationProgress}%)`, '📦');
        }
    }

    /**
     * ترحيل المنتجات (المواد الخام)
     */
    async migrateProducts() {
        try {
            console.log('📦 ترحيل المواد الخام...');
            
            const rawMaterials = JSON.parse(localStorage.getItem('products') || '[]');
            
            for (const product of rawMaterials) {
                await this.migrateProduct(product, 'مواد خام');
                this.updateProgress('products', `ترحيل المنتج: ${product.name}`);
            }

            this.migrationStatus.products = true;
            console.log(`✅ تم ترحيل ${rawMaterials.length} مادة خام`);
        } catch (error) {
            console.error('خطأ في ترحيل المواد الخام:', error);
            throw error;
        }
    }

    /**
     * ترحيل المنتجات المصنعة
     */
    async migrateFinishedProducts() {
        try {
            console.log('🍹 ترحيل المنتجات المصنعة...');
            
            const finishedProducts = JSON.parse(localStorage.getItem('finishedProducts') || '[]');
            
            for (const product of finishedProducts) {
                await this.migrateProduct(product, 'منتجات مصنعة');
                this.updateProgress('finishedProducts', `ترحيل المنتج: ${product.name}`);
            }

            this.migrationStatus.finishedProducts = true;
            console.log(`✅ تم ترحيل ${finishedProducts.length} منتج مصنع`);
        } catch (error) {
            console.error('خطأ في ترحيل المنتجات المصنعة:', error);
            throw error;
        }
    }

    /**
     * ترحيل منتج واحد
     */
    async migrateProduct(product, category) {
        try {
            const sql = `
                INSERT OR IGNORE INTO products (
                    name, category, price, cost, stock, min_stock, max_stock,
                    barcode, description, unit, is_featured, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const createdAt = product.createdAt || new Date().toISOString();

            hybridDB.run(sql, [
                product.name,
                category,
                product.price || product.sellingPrice || 0,
                product.cost || product.costPrice || 0,
                product.stock || product.quantity || 0,
                product.minStock || 5,
                product.maxStock || 1000,
                product.barcode || null,
                product.description || '',
                product.unit || 'قطعة',
                product.isFeatured || false,
                createdAt,
                createdAt
            ]);

            // إضافة حركة مخزون أولية إذا كان هناك مخزون
            const quantity = product.stock || product.quantity || 0;
            if (quantity > 0) {
                const movementSQL = `
                    INSERT INTO inventory_movements (
                        product_name, movement_type, quantity, old_quantity, new_quantity,
                        reference_type, notes, movement_date, user_name
                    ) VALUES (?, 'in', ?, 0, ?, 'migration', 'ترحيل من النظام القديم', ?, 'النظام')
                `;
                
                hybridDB.run(movementSQL, [
                    product.name,
                    quantity,
                    quantity,
                    createdAt
                ]);
            }
        } catch (error) {
            console.error(`خطأ في ترحيل المنتج ${product.name}:`, error);
        }
    }

    /**
     * ترحيل العملاء
     */
    async migrateCustomers() {
        try {
            console.log('👥 ترحيل العملاء...');
            
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            
            for (const customer of customers) {
                await this.migrateCustomer(customer);
                this.updateProgress('customers', `ترحيل العميل: ${customer.name}`);
            }

            this.migrationStatus.customers = true;
            console.log(`✅ تم ترحيل ${customers.length} عميل`);
        } catch (error) {
            console.error('خطأ في ترحيل العملاء:', error);
            throw error;
        }
    }

    /**
     * ترحيل عميل واحد
     */
    async migrateCustomer(customer) {
        try {
            const sql = `
                INSERT OR IGNORE INTO customers (
                    name, phone, email, address, city, type, credit_limit, credit_days,
                    total_spent, total_purchases, loyalty_points, total_earned_points,
                    total_redeemed_points, status, last_purchase_date, registration_date,
                    birth_date, gender, notes, discount_percentage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                customer.name,
                customer.phone || null,
                customer.email || null,
                customer.address || '',
                customer.city || '',
                customer.type || 'عادي',
                customer.creditLimit || 1000,
                customer.creditDays || 30,
                customer.totalSpent || 0,
                customer.totalPurchases || customer.purchaseCount || 0,
                customer.loyaltyPoints || 0,
                customer.totalEarnedPoints || 0,
                customer.totalRedeemedPoints || 0,
                customer.status || 'نشط',
                customer.lastPurchase || customer.lastPurchaseDate || null,
                customer.registrationDate || new Date().toISOString(),
                customer.birthDate || null,
                customer.gender || null,
                customer.notes || '',
                customer.discountPercentage || 0
            ]);

            // ترحيل تاريخ نقاط الولاء إذا كان موجوداً
            if (customer.pointsHistory && customer.pointsHistory.length > 0) {
                for (const pointTransaction of customer.pointsHistory) {
                    await this.migrateLoyaltyTransaction(customer.name, pointTransaction);
                }
            }
        } catch (error) {
            console.error(`خطأ في ترحيل العميل ${customer.name}:`, error);
        }
    }

    /**
     * ترحيل معاملة نقاط ولاء
     */
    async migrateLoyaltyTransaction(customerName, transaction) {
        try {
            // الحصول على معرف العميل
            const customer = hybridDB.query(
                'SELECT id FROM customers WHERE name = ?',
                [customerName]
            );

            if (!customer) return;

            const sql = `
                INSERT INTO loyalty_transactions (
                    customer_id, transaction_type, points, reference_type, 
                    description, transaction_date
                ) VALUES (?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                customer.id,
                transaction.type || 'earned',
                transaction.points || 0,
                transaction.source || 'migration',
                transaction.description || transaction.reason || 'ترحيل من النظام القديم',
                transaction.date || new Date().toISOString()
            ]);
        } catch (error) {
            console.error('خطأ في ترحيل معاملة نقاط الولاء:', error);
        }
    }

    /**
     * ترحيل الموردين
     */
    async migrateSuppliers() {
        try {
            console.log('🏪 ترحيل الموردين...');
            
            const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
            
            for (const supplier of suppliers) {
                await this.migrateSupplier(supplier);
                this.updateProgress('suppliers', `ترحيل المورد: ${supplier.name}`);
            }

            this.migrationStatus.suppliers = true;
            console.log(`✅ تم ترحيل ${suppliers.length} مورد`);
        } catch (error) {
            console.error('خطأ في ترحيل الموردين:', error);
            throw error;
        }
    }

    /**
     * ترحيل مورد واحد
     */
    async migrateSupplier(supplier) {
        try {
            const sql = `
                INSERT OR IGNORE INTO suppliers (
                    name, contact_person, phone, email, address, city, country,
                    total_purchases, last_purchase_date, status, payment_terms,
                    credit_limit, notes, rating, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                supplier.name,
                supplier.contactPerson || supplier.contact || '',
                supplier.phone || null,
                supplier.email || null,
                supplier.address || '',
                supplier.city || '',
                supplier.country || 'فلسطين',
                supplier.totalPurchases || 0,
                supplier.lastPurchaseDate || null,
                supplier.status || 'نشط',
                supplier.paymentTerms || '',
                supplier.creditLimit || 0,
                supplier.notes || '',
                supplier.rating || 5,
                supplier.createdAt || new Date().toISOString()
            ]);
        } catch (error) {
            console.error(`خطأ في ترحيل المورد ${supplier.name}:`, error);
        }
    }

    /**
     * ترحيل المبيعات
     */
    async migrateSales() {
        try {
            console.log('💰 ترحيل المبيعات...');
            
            const sales = JSON.parse(localStorage.getItem('sales') || '[]');
            
            for (const sale of sales) {
                await this.migrateSale(sale);
                this.updateProgress('sales', `ترحيل المبيعة: ${sale.invoiceNumber || sale.id}`);
            }

            this.migrationStatus.sales = true;
            console.log(`✅ تم ترحيل ${sales.length} عملية بيع`);
        } catch (error) {
            console.error('خطأ في ترحيل المبيعات:', error);
            throw error;
        }
    }

    /**
     * ترحيل عملية بيع واحدة
     */
    async migrateSale(sale) {
        try {
            // البحث عن العميل
            let customerId = null;
            if (sale.customerName && sale.customerName !== 'عميل نقدي') {
                const customer = hybridDB.query(
                    'SELECT id FROM customers WHERE name = ?',
                    [sale.customerName]
                );
                customerId = customer ? customer.id : null;
            }

            // إضافة المبيعة
            const saleSQL = `
                INSERT OR IGNORE INTO sales (
                    invoice_number, customer_id, customer_name, total_amount, 
                    discount_amount, discount_percentage, tax_amount, tax_percentage,
                    final_amount, payment_method, payment_status, due_date,
                    sale_date, cashier_name, notes, is_refunded
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const invoiceNumber = sale.invoiceNumber || sale.id || `INV-${Date.now()}`;

            hybridDB.run(saleSQL, [
                invoiceNumber,
                customerId,
                sale.customerName || 'عميل نقدي',
                sale.subtotal || sale.total || 0,
                sale.discountAmount || sale.discount || 0,
                sale.discountPercentage || 0,
                sale.taxAmount || sale.tax || 0,
                sale.taxPercentage || 0,
                sale.total || sale.finalAmount || 0,
                sale.paymentMethod || 'نقدي',
                sale.paymentMethod === 'آجل' ? 'غير مدفوع' : 'مدفوع',
                sale.dueDate || null,
                sale.date || sale.saleDate || new Date().toISOString(),
                sale.cashier || 'النظام',
                sale.notes || '',
                sale.isRefunded || false
            ]);

            // الحصول على معرف المبيعة
            const saleId = hybridDB.query('SELECT last_insert_rowid() as id').id;

            // ترحيل عناصر المبيعة
            if (sale.items && sale.items.length > 0) {
                for (const item of sale.items) {
                    await this.migrateSaleItem(saleId, item);
                }
            }
        } catch (error) {
            console.error(`خطأ في ترحيل المبيعة ${sale.invoiceNumber}:`, error);
        }
    }

    /**
     * ترحيل عنصر مبيعة
     */
    async migrateSaleItem(saleId, item) {
        try {
            // البحث عن المنتج
            let productId = null;
            const product = hybridDB.query(
                'SELECT id, cost FROM products WHERE name = ?',
                [item.name]
            );
            
            if (product) {
                productId = product.id;
            }

            const costPrice = product ? product.cost : 0;
            const totalPrice = item.price * item.quantity;
            const profit = totalPrice - (costPrice * item.quantity);

            const sql = `
                INSERT INTO sale_items (
                    sale_id, product_id, product_name, quantity, unit_price, 
                    total_price, cost_price, profit, discount_amount, tax_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            hybridDB.run(sql, [
                saleId,
                productId,
                item.name,
                item.quantity,
                item.price,
                totalPrice,
                costPrice,
                profit,
                item.discountAmount || 0,
                item.taxAmount || 0
            ]);
        } catch (error) {
            console.error(`خطأ في ترحيل عنصر المبيعة ${item.name}:`, error);
        }
    }

    /**
     * ترحيل الإعدادات
     */
    async migrateSettings() {
        try {
            console.log('⚙️ ترحيل الإعدادات...');
            
            // ترحيل إعدادات نظام الولاء
            const loyaltySettings = localStorage.getItem('loyaltySettings');
            if (loyaltySettings) {
                hybridDB.run(
                    'INSERT OR REPLACE INTO settings (key, value, description, category) VALUES (?, ?, ?, ?)',
                    ['loyaltySettings', loyaltySettings, 'إعدادات نظام الولاء', 'loyalty']
                );
                this.updateProgress('settings', 'ترحيل إعدادات الولاء');
            }

            // ترحيل إعدادات أخرى
            const settingsKeys = ['taxRate', 'companyInfo', 'printSettings', 'backupSettings'];
            for (const key of settingsKeys) {
                const value = localStorage.getItem(key);
                if (value) {
                    hybridDB.run(
                        'INSERT OR REPLACE INTO settings (key, value, description, category) VALUES (?, ?, ?, ?)',
                        [key, value, `إعدادات ${key}`, 'general']
                    );
                    this.updateProgress('settings', `ترحيل إعدادات ${key}`);
                }
            }

            this.migrationStatus.settings = true;
            console.log('✅ تم ترحيل الإعدادات');
        } catch (error) {
            console.error('خطأ في ترحيل الإعدادات:', error);
            throw error;
        }
    }

    /**
     * إنشاء نسخة احتياطية من localStorage
     */
    backupLocalStorageData() {
        try {
            console.log('💾 إنشاء نسخة احتياطية من localStorage...');
            
            const backup = {};
            const keys = ['products', 'customers', 'sales', 'suppliers', 'finishedProducts'];
            
            for (const key of keys) {
                const data = localStorage.getItem(key);
                if (data) {
                    backup[key] = data;
                }
            }

            localStorage.setItem('localStorage_backup_v2', JSON.stringify({
                data: backup,
                backupDate: new Date().toISOString(),
                version: 'pre-hybrid-v2',
                migrationVersion: '2.0'
            }));

            this.updateProgress('backup', 'إنشاء نسخة احتياطية');
            console.log('💾 تم إنشاء نسخة احتياطية من localStorage');
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        }
    }

    /**
     * تحديد الترحيل كمكتمل
     */
    markMigrationComplete() {
        localStorage.setItem('hybridMigrationComplete_v2', JSON.stringify({
            completed: true,
            date: new Date().toISOString(),
            version: '2.0',
            status: this.migrationStatus,
            progress: 100
        }));
        
        this.updateProgress('complete', 'اكتمال الترحيل');
        console.log('✅ تم تحديد الترحيل كمكتمل');
    }

    /**
     * التحقق من اكتمال الترحيل
     */
    isMigrationComplete() {
        const status = localStorage.getItem('hybridMigrationComplete_v2');
        return status ? JSON.parse(status).completed : false;
    }

    /**
     * الحصول على تقدم الترحيل
     */
    getMigrationProgress() {
        const status = localStorage.getItem('hybridMigrationComplete_v2');
        if (status) {
            const parsed = JSON.parse(status);
            return parsed.progress || 100;
        }
        return this.migrationProgress;
    }

    /**
     * إعادة تعيين حالة الترحيل (للاختبار)
     */
    resetMigration() {
        localStorage.removeItem('hybridMigrationComplete_v2');
        this.migrationStatus = {
            products: false,
            customers: false,
            sales: false,
            suppliers: false,
            settings: false,
            finishedProducts: false
        };
        this.migrationProgress = 0;
        this.currentStep = 0;
        console.log('🔄 تم إعادة تعيين حالة الترحيل');
    }
}

// إنشاء مثيل من نظام الترحيل
const dataMigration = new DataMigration();

// تصدير للاستخدام العام
window.dataMigration = dataMigration;
