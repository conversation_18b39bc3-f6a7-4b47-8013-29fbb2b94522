/**
 * نظام نقطة البيع للنسخة الهجين v2.0
 * محسن للأداء والسرعة
 */

class POSSystem {
    constructor() {
        this.cart = [];
        this.currentCustomer = null;
        this.discount = 0;
        this.tax = 16; // 16%
        this.isInitialized = false;
    }

    /**
     * تهيئة نظام نقطة البيع
     */
    async initialize() {
        if (this.isInitialized) return;
        
        console.log('💰 تهيئة نظام نقطة البيع الهجين...');
        
        // إنشاء واجهة نقطة البيع
        this.createPOSInterface();
        
        // تحميل البيانات الأولية
        await this.loadInitialData();
        
        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام نقطة البيع');
    }

    /**
     * إنشاء واجهة نقطة البيع
     */
    createPOSInterface() {
        const posHTML = `
            <div class="pos-container" style="display: none;" id="posContainer">
                <div class="pos-header">
                    <h2>💰 نقطة البيع الهجين</h2>
                    <button class="btn btn-secondary" onclick="closePOS()">إغلاق</button>
                </div>
                
                <div class="pos-content">
                    <!-- قسم البحث والمنتجات -->
                    <div class="pos-left">
                        <div class="search-section">
                            <input type="text" id="productSearch" placeholder="ابحث عن منتج..." 
                                   onkeyup="searchProducts(this.value)" class="form-control">
                        </div>
                        
                        <div class="products-grid" id="productsGrid">
                            <!-- المنتجات ستظهر هنا -->
                        </div>
                    </div>
                    
                    <!-- قسم السلة والدفع -->
                    <div class="pos-right">
                        <div class="customer-section">
                            <label>العميل:</label>
                            <select id="customerSelect" class="form-control" onchange="selectCustomer(this.value)">
                                <option value="">عميل نقدي</option>
                            </select>
                        </div>
                        
                        <div class="cart-section">
                            <h4>السلة</h4>
                            <div class="cart-items" id="cartItems">
                                <!-- عناصر السلة ستظهر هنا -->
                            </div>
                        </div>
                        
                        <div class="totals-section">
                            <div class="total-line">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0.00 ₪</span>
                            </div>
                            <div class="total-line">
                                <span>الخصم:</span>
                                <input type="number" id="discountInput" value="0" min="0" max="100" 
                                       onchange="updateTotals()" style="width: 80px;"> %
                            </div>
                            <div class="total-line">
                                <span>الضريبة (16%):</span>
                                <span id="taxAmount">0.00 ₪</span>
                            </div>
                            <div class="total-line total-final">
                                <span>المجموع النهائي:</span>
                                <span id="finalTotal">0.00 ₪</span>
                            </div>
                        </div>
                        
                        <div class="payment-section">
                            <label>طريقة الدفع:</label>
                            <select id="paymentMethod" class="form-control">
                                <option value="نقدي">نقدي</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="آجل">آجل</option>
                            </select>
                            
                            <button class="btn btn-success btn-large" onclick="completeSale()" 
                                    style="width: 100%; margin-top: 1rem; padding: 1rem;">
                                إتمام البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const posStyles = `
            <style>
                .pos-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: white;
                    z-index: 10000;
                    overflow-y: auto;
                }
                
                .pos-header {
                    background: #2c3e50;
                    color: white;
                    padding: 1rem 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .pos-content {
                    display: flex;
                    height: calc(100vh - 80px);
                }
                
                .pos-left {
                    flex: 2;
                    padding: 1rem;
                    border-right: 1px solid #ddd;
                }
                
                .pos-right {
                    flex: 1;
                    padding: 1rem;
                    background: #f8f9fa;
                }
                
                .search-section {
                    margin-bottom: 1rem;
                }
                
                .products-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                    gap: 1rem;
                    max-height: calc(100vh - 200px);
                    overflow-y: auto;
                }
                
                .product-card {
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 1rem;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .product-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }
                
                .product-name {
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                }
                
                .product-price {
                    color: #28a745;
                    font-size: 1.1rem;
                    font-weight: bold;
                }
                
                .product-stock {
                    font-size: 0.8rem;
                    color: #6c757d;
                }
                
                .customer-section, .cart-section, .totals-section, .payment-section {
                    margin-bottom: 1.5rem;
                }
                
                .cart-items {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 0.5rem;
                    background: white;
                }
                
                .cart-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.5rem;
                    border-bottom: 1px solid #eee;
                }
                
                .cart-item:last-child {
                    border-bottom: none;
                }
                
                .total-line {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.25rem 0;
                }
                
                .total-final {
                    font-weight: bold;
                    font-size: 1.2rem;
                    border-top: 2px solid #ddd;
                    padding-top: 0.5rem;
                    margin-top: 0.5rem;
                }
                
                .btn-large {
                    font-size: 1.2rem;
                    font-weight: bold;
                }
            </style>
        `;

        // إضافة الواجهة والأنماط إلى الصفحة
        document.head.insertAdjacentHTML('beforeend', posStyles);
        document.body.insertAdjacentHTML('beforeend', posHTML);
    }

    /**
     * تحميل البيانات الأولية
     */
    async loadInitialData() {
        // تحميل المنتجات
        await this.loadProducts();
        
        // تحميل العملاء
        await this.loadCustomers();
    }

    /**
     * تحميل المنتجات
     */
    async loadProducts() {
        try {
            const products = dbAPI.getProducts(true);
            this.displayProducts(products);
        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
        }
    }

    /**
     * عرض المنتجات
     */
    displayProducts(products) {
        const grid = document.getElementById('productsGrid');
        if (!grid) return;

        grid.innerHTML = products.map(product => `
            <div class="product-card" onclick="addToCart('${product.name}', ${product.price}, ${product.stock})">
                <div class="product-name">${product.name}</div>
                <div class="product-price">${product.price.toFixed(2)} ₪</div>
                <div class="product-stock">المخزون: ${product.stock}</div>
            </div>
        `).join('');
    }

    /**
     * تحميل العملاء
     */
    async loadCustomers() {
        try {
            const customers = dbAPI.getCustomers(true);
            this.displayCustomers(customers);
        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
        }
    }

    /**
     * عرض العملاء في القائمة المنسدلة
     */
    displayCustomers(customers) {
        const select = document.getElementById('customerSelect');
        if (!select) return;

        const options = customers.map(customer => 
            `<option value="${customer.name}">${customer.name} - ${customer.phone || 'بدون هاتف'}</option>`
        ).join('');

        select.innerHTML = '<option value="">عميل نقدي</option>' + options;
    }

    /**
     * إضافة منتج إلى السلة
     */
    addToCart(productName, price, stock) {
        if (stock <= 0) {
            alert('المنتج غير متوفر في المخزون');
            return;
        }

        const existingItem = this.cart.find(item => item.name === productName);
        
        if (existingItem) {
            if (existingItem.quantity >= stock) {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            existingItem.quantity++;
        } else {
            this.cart.push({
                name: productName,
                price: price,
                quantity: 1,
                stock: stock
            });
        }

        this.updateCartDisplay();
        this.updateTotals();
    }

    /**
     * تحديث عرض السلة
     */
    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        if (!cartItems) return;

        if (this.cart.length === 0) {
            cartItems.innerHTML = '<div style="text-align: center; color: #6c757d;">السلة فارغة</div>';
            return;
        }

        cartItems.innerHTML = this.cart.map((item, index) => `
            <div class="cart-item">
                <div>
                    <strong>${item.name}</strong><br>
                    <small>${item.price.toFixed(2)} ₪ × ${item.quantity}</small>
                </div>
                <div>
                    <button onclick="changeQuantity(${index}, -1)" class="btn btn-sm btn-outline-secondary">-</button>
                    <span style="margin: 0 0.5rem;">${item.quantity}</span>
                    <button onclick="changeQuantity(${index}, 1)" class="btn btn-sm btn-outline-secondary">+</button>
                    <button onclick="removeFromCart(${index})" class="btn btn-sm btn-outline-danger" style="margin-left: 0.5rem;">×</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تغيير كمية المنتج
     */
    changeQuantity(index, change) {
        const item = this.cart[index];
        const newQuantity = item.quantity + change;

        if (newQuantity <= 0) {
            this.removeFromCart(index);
            return;
        }

        if (newQuantity > item.stock) {
            alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            return;
        }

        item.quantity = newQuantity;
        this.updateCartDisplay();
        this.updateTotals();
    }

    /**
     * إزالة منتج من السلة
     */
    removeFromCart(index) {
        this.cart.splice(index, 1);
        this.updateCartDisplay();
        this.updateTotals();
    }

    /**
     * تحديث المجاميع
     */
    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discountPercent = parseFloat(document.getElementById('discountInput')?.value || 0);
        const discountAmount = subtotal * (discountPercent / 100);
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = taxableAmount * (this.tax / 100);
        const finalTotal = taxableAmount + taxAmount;

        document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ₪';
        document.getElementById('taxAmount').textContent = taxAmount.toFixed(2) + ' ₪';
        document.getElementById('finalTotal').textContent = finalTotal.toFixed(2) + ' ₪';
    }

    /**
     * اختيار عميل
     */
    selectCustomer(customerName) {
        this.currentCustomer = customerName || null;
    }

    /**
     * إتمام البيع
     */
    async completeSale() {
        if (this.cart.length === 0) {
            alert('السلة فارغة');
            return;
        }

        try {
            const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountPercent = parseFloat(document.getElementById('discountInput')?.value || 0);
            const discountAmount = subtotal * (discountPercent / 100);
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * (this.tax / 100);
            const finalTotal = taxableAmount + taxAmount;
            const paymentMethod = document.getElementById('paymentMethod')?.value || 'نقدي';

            const sale = {
                invoiceNumber: 'INV-' + Date.now(),
                customerName: this.currentCustomer || 'عميل نقدي',
                items: this.cart,
                subtotal: subtotal,
                discountAmount: discountAmount,
                discountPercentage: discountPercent,
                taxAmount: taxAmount,
                taxPercentage: this.tax,
                total: finalTotal,
                paymentMethod: paymentMethod,
                date: new Date().toISOString(),
                cashier: 'النظام الهجين'
            };

            // حفظ البيع
            const success = await dbAPI.addSale(sale);
            
            if (success) {
                alert('تم إتمام البيع بنجاح!');
                this.clearCart();
                this.closePOS();
            } else {
                alert('حدث خطأ في حفظ البيع');
            }
        } catch (error) {
            console.error('خطأ في إتمام البيع:', error);
            alert('حدث خطأ في إتمام البيع');
        }
    }

    /**
     * مسح السلة
     */
    clearCart() {
        this.cart = [];
        this.currentCustomer = null;
        this.updateCartDisplay();
        this.updateTotals();
        document.getElementById('customerSelect').value = '';
        document.getElementById('discountInput').value = '0';
        document.getElementById('paymentMethod').value = 'نقدي';
    }

    /**
     * إغلاق نقطة البيع
     */
    closePOS() {
        document.getElementById('posContainer').style.display = 'none';
    }

    /**
     * فتح نقطة البيع
     */
    async openPOS() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        document.getElementById('posContainer').style.display = 'block';
        await this.loadProducts();
        await this.loadCustomers();
    }
}

// إنشاء مثيل من نظام نقطة البيع
const posSystem = new POSSystem();

// الوظائف العامة
window.startPOS = async function() {
    await posSystem.openPOS();
};

window.closePOS = function() {
    posSystem.closePOS();
};

window.addToCart = function(productName, price, stock) {
    posSystem.addToCart(productName, price, stock);
};

window.changeQuantity = function(index, change) {
    posSystem.changeQuantity(index, change);
};

window.removeFromCart = function(index) {
    posSystem.removeFromCart(index);
};

window.updateTotals = function() {
    posSystem.updateTotals();
};

window.selectCustomer = function(customerName) {
    posSystem.selectCustomer(customerName);
};

window.completeSale = async function() {
    await posSystem.completeSale();
};

window.searchProducts = function(searchTerm) {
    if (searchTerm.length < 2) {
        posSystem.loadProducts();
        return;
    }
    
    const products = dbAPI.searchProducts(searchTerm);
    posSystem.displayProducts(products);
};

// تصدير للاستخدام العام
window.posSystem = posSystem;
