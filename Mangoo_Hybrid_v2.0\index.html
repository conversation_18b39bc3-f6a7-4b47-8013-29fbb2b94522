<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Mangoo Hybrid - نظام إدارة محل العصائر والحلويات الهجين</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">

    <style>
        /* إعادة تعيين الأنماط */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
            text-align: right;
            line-height: 1.4;
            font-size: 14px;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* الحاوي الرئيسي */
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* شريط التنقل العلوي المحسن */
        .navbar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .nav-brand::before {
            content: "🚀";
            font-size: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-center {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255,255,255,0.1);
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: 2px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .system-status.hybrid {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-color: rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .system-status.fallback {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border-color: rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.9rem;
        }

        .developer-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            background: rgba(255,255,255,0.15);
            padding: 0.75rem 1.25rem;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .developer-name {
            font-size: 1rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .developer-contact {
            font-size: 0.8rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* بطاقات الترحيب */
        .welcome-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .welcome-subtitle {
            font-size: 1.3rem;
            color: #6c757d;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
        }

        .feature-description {
            color: #6c757d;
            line-height: 1.6;
            font-size: 1rem;
        }

        /* أزرار العمل */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        /* حالة التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 90%;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .loading-details {
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .nav-brand {
                font-size: 1.2rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1.1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* إخفاء العناصر */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- شريط التنقل العلوي -->
        <nav class="navbar">
            <div class="nav-brand">
                Mangoo Hybrid System
            </div>
            
            <div class="nav-center">
                <div class="system-status" id="systemStatus">
                    <span id="statusIcon">🔄</span>
                    <span id="statusText">جاري التهيئة...</span>
                </div>
            </div>
            
            <div class="nav-user">
                <div class="developer-info">
                    <div class="developer-name">💻 Fares Nawaf</div>
                    <div class="developer-contact">📞 0569329925</div>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- قسم الترحيب -->
            <section class="welcome-section">
                <h1 class="welcome-title">🚀 مرحباً بك في النظام الهجين</h1>
                <p class="welcome-subtitle">
                    نظام متطور يجمع بين سهولة الاستخدام وقوة قواعد البيانات المتقدمة
                </p>

                <!-- أزرار العمل الرئيسية -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="startPOS()">
                        💰 نقطة البيع
                    </button>
                    <button class="btn btn-success" onclick="manageInventory()">
                        📦 إدارة المخزون
                    </button>
                    <button class="btn btn-info" onclick="manageCustomers()">
                        👥 إدارة العملاء
                    </button>
                    <button class="btn btn-warning" onclick="viewReports()">
                        📊 التقارير المتقدمة
                    </button>
                </div>
            </section>

            <!-- مزايا النظام الهجين -->
            <section class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3 class="feature-title">أداء فائق</h3>
                    <p class="feature-description">
                        أسرع 10x من النظام التقليدي مع قاعدة بيانات SQLite محلية
                    </p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">📊</span>
                    <h3 class="feature-title">تقارير متقدمة</h3>
                    <p class="feature-description">
                        تحليلات عميقة ورؤى تجارية قيمة مع استعلامات SQL معقدة
                    </p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🛡️</span>
                    <h3 class="feature-title">أمان محسن</h3>
                    <p class="feature-description">
                        نسخ احتياطي تلقائي وحماية متقدمة للبيانات الحساسة
                    </p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🔄</span>
                    <h3 class="feature-title">نظام احتياطي ذكي</h3>
                    <p class="feature-description">
                        تبديل تلقائي للنظام الاحتياطي عند الحاجة بدون انقطاع الخدمة
                    </p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <h3 class="feature-title">محمولية كاملة</h3>
                    <p class="feature-description">
                        يعمل من أي مجلد أو فلاشة بدون تثبيت أو إعدادات معقدة
                    </p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <h3 class="feature-title">سهولة الاستخدام</h3>
                    <p class="feature-description">
                        واجهة مألوفة مع تحسينات خفية تجعل العمل أسرع وأكثر كفاءة
                    </p>
                </div>
            </section>
        </main>
    </div>

    <!-- شاشة التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loadingText">جاري تهيئة النظام الهجين...</div>
            <div class="loading-details" id="loadingDetails">يرجى الانتظار</div>
        </div>
    </div>

    <!-- النظام الهجين - قاعدة البيانات المتقدمة -->
    <script src="database.js"></script>
    <script src="database-api.js"></script>
    <script src="data-migration.js"></script>
    <script src="advanced-reports.js"></script>
    <script src="pos-system.js"></script>
    <script src="inventory-management.js"></script>
    <script src="customer-management.js"></script>

    <!-- تهيئة النظام الهجين -->
    <script>
        // متغيرات النظام
        let systemReady = false;
        let isHybridMode = false;

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 بدء تهيئة النظام الهجين...');
            
            try {
                await initializeHybridSystem();
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                showFallbackMode();
            }
        });

        /**
         * تهيئة النظام الهجين
         */
        async function initializeHybridSystem() {
            updateLoadingStatus('تحميل مكتبات قاعدة البيانات...', '🔄');
            
            // تهيئة قاعدة البيانات
            const dbInitialized = await dbAPI.init();
            
            if (dbInitialized) {
                updateLoadingStatus('ترحيل البيانات...', '📦');
                
                // ترحيل البيانات إذا لم يتم من قبل
                if (!dataMigration.isMigrationComplete()) {
                    await dataMigration.migrateAllData();
                }
                
                updateLoadingStatus('تحميل البيانات...', '📊');
                await loadInitialData();
                
                updateLoadingStatus('تهيئة الواجهات...', '🎨');
                await initializeInterfaces();
                
                showHybridMode();
                console.log('✅ تم تهيئة النظام الهجين بنجاح');
            } else {
                showFallbackMode();
                console.log('⚠️ تم التبديل إلى النظام الاحتياطي');
            }
            
            systemReady = true;
            hideLoadingOverlay();
        }

        /**
         * تحديث حالة التحميل
         */
        function updateLoadingStatus(text, icon = '🔄') {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingDetails').textContent = icon + ' ' + text;
        }

        /**
         * عرض النظام الهجين
         */
        function showHybridMode() {
            isHybridMode = true;
            const statusElement = document.getElementById('systemStatus');
            statusElement.className = 'system-status hybrid';
            document.getElementById('statusIcon').textContent = '🚀';
            document.getElementById('statusText').textContent = 'النظام الهجين نشط';
        }

        /**
         * عرض النظام الاحتياطي
         */
        function showFallbackMode() {
            isHybridMode = false;
            const statusElement = document.getElementById('systemStatus');
            statusElement.className = 'system-status fallback';
            document.getElementById('statusIcon').textContent = '⚠️';
            document.getElementById('statusText').textContent = 'النظام الاحتياطي';
        }

        /**
         * إخفاء شاشة التحميل
         */
        function hideLoadingOverlay() {
            setTimeout(() => {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }, 1000);
        }

        /**
         * تحميل البيانات الأولية
         */
        async function loadInitialData() {
            // سيتم تنفيذ هذا في الملفات المنفصلة
            console.log('📊 تحميل البيانات الأولية...');
        }

        /**
         * تهيئة الواجهات
         */
        async function initializeInterfaces() {
            // سيتم تنفيذ هذا في الملفات المنفصلة
            console.log('🎨 تهيئة الواجهات...');
        }

        // وظائف الأزرار الرئيسية
        function startPOS() {
            if (!systemReady) {
                alert('النظام لا يزال قيد التهيئة، يرجى الانتظار...');
                return;
            }
            // سيتم تنفيذ هذا في pos-system.js
            console.log('💰 بدء نقطة البيع...');
        }

        function manageInventory() {
            if (!systemReady) {
                alert('النظام لا يزال قيد التهيئة، يرجى الانتظار...');
                return;
            }
            // سيتم تنفيذ هذا في inventory-management.js
            console.log('📦 إدارة المخزون...');
        }

        function manageCustomers() {
            if (!systemReady) {
                alert('النظام لا يزال قيد التهيئة، يرجى الانتظار...');
                return;
            }
            // سيتم تنفيذ هذا في customer-management.js
            console.log('👥 إدارة العملاء...');
        }

        function viewReports() {
            if (!systemReady) {
                alert('النظام لا يزال قيد التهيئة، يرجى الانتظار...');
                return;
            }
            // سيتم تنفيذ هذا في advanced-reports.js
            console.log('📊 التقارير المتقدمة...');
        }
    </script>
</body>
</html>
